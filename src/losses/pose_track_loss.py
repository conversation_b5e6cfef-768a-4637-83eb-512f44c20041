import torch
import torch.nn as nn
import torch.nn.functional as F


class FocalLoss(nn.Module):
    """Focal Loss for addressing class imbalance"""
    
    def __init__(self, alpha=1, gamma=2, reduction='mean'):
        super(<PERSON>oc<PERSON><PERSON><PERSON>, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class KeypointLoss(nn.Module):
    """Loss for keypoint heatmap prediction"""
    
    def __init__(self, use_target_weight=True):
        super(KeypointLoss, self).__init__()
        self.use_target_weight = use_target_weight
        self.mse_loss = nn.MSELoss(reduction='none')
    
    def forward(self, pred_heatmaps, target_heatmaps, target_weights=None):
        """
        Args:
            pred_heatmaps: Predicted heatmaps [B, K, H, W]
            target_heatmaps: Target heatmaps [B, K, H, W]
            target_weights: Weights for each keypoint [B, K, 1]
        """
        batch_size, num_keypoints = pred_heatmaps.shape[:2]
        
        loss = self.mse_loss(pred_heatmaps, target_heatmaps)
        
        if self.use_target_weight and target_weights is not None:
            # Expand target weights to match heatmap dimensions
            target_weights = target_weights.unsqueeze(-1)  # [B, K, 1, 1]
            loss = loss * target_weights
        
        return loss.mean()


class PAFLoss(nn.Module):
    """Loss for Part Affinity Fields"""
    
    def __init__(self, use_target_weight=True):
        super(PAFLoss, self).__init__()
        self.use_target_weight = use_target_weight
        self.mse_loss = nn.MSELoss(reduction='none')
    
    def forward(self, pred_pafs, target_pafs, target_weights=None):
        """
        Args:
            pred_pafs: Predicted PAFs [B, 2*L, H, W] where L is number of limbs
            target_pafs: Target PAFs [B, 2*L, H, W]
            target_weights: Weights for each limb [B, L, 1]
        """
        loss = self.mse_loss(pred_pafs, target_pafs)
        
        if self.use_target_weight and target_weights is not None:
            # Expand weights for x and y components
            target_weights = target_weights.repeat(1, 2, 1, 1)  # [B, 2*L, 1, 1]
            loss = loss * target_weights
        
        return loss.mean()


class InstanceLoss(nn.Module):
    """Loss for instance segmentation"""
    
    def __init__(self, pos_weight=None):
        super(InstanceLoss, self).__init__()
        self.bce_loss = nn.BCEWithLogitsLoss(pos_weight=pos_weight)
    
    def forward(self, pred_instances, target_instances):
        """
        Args:
            pred_instances: Predicted instance maps [B, 1, H, W]
            target_instances: Target instance maps [B, 1, H, W]
        """
        return self.bce_loss(pred_instances, target_instances)


class TrackingLoss(nn.Module):
    """Loss for tracking embeddings using triplet loss"""
    
    def __init__(self, margin=0.3, mining='hard'):
        super(TrackingLoss, self).__init__()
        self.margin = margin
        self.mining = mining
        self.triplet_loss = nn.TripletMarginLoss(margin=margin, p=2)
    
    def forward(self, embeddings, track_ids):
        """
        Args:
            embeddings: Person embeddings [N, D]
            track_ids: Track IDs for each embedding [N]
        """
        if len(embeddings) < 2:
            return torch.tensor(0.0, device=embeddings.device)
        
        unique_ids = torch.unique(track_ids)
        if len(unique_ids) < 2:
            return torch.tensor(0.0, device=embeddings.device)
        
        if self.mining == 'hard':
            return self._hard_triplet_loss(embeddings, track_ids)
        else:
            return self._random_triplet_loss(embeddings, track_ids)
    
    def _hard_triplet_loss(self, embeddings, track_ids):
        """Hard negative mining triplet loss"""
        losses = []
        
        for track_id in torch.unique(track_ids):
            # Get positive samples
            pos_mask = track_ids == track_id
            pos_embeddings = embeddings[pos_mask]
            
            if len(pos_embeddings) < 2:
                continue
            
            # Get negative samples
            neg_mask = track_ids != track_id
            neg_embeddings = embeddings[neg_mask]
            
            if len(neg_embeddings) == 0:
                continue
            
            # For each positive pair, find hardest negative
            for i in range(len(pos_embeddings)):
                anchor = pos_embeddings[i]
                
                # Find hardest positive (most distant positive sample)
                pos_distances = torch.norm(anchor.unsqueeze(0) - pos_embeddings, p=2, dim=1)
                pos_distances[i] = -1  # Exclude anchor itself
                hardest_pos_idx = torch.argmax(pos_distances)
                positive = pos_embeddings[hardest_pos_idx]
                
                # Find hardest negative (closest negative sample)
                neg_distances = torch.norm(anchor.unsqueeze(0) - neg_embeddings, p=2, dim=1)
                hardest_neg_idx = torch.argmin(neg_distances)
                negative = neg_embeddings[hardest_neg_idx]
                
                # Compute triplet loss
                loss = self.triplet_loss(
                    anchor.unsqueeze(0),
                    positive.unsqueeze(0),
                    negative.unsqueeze(0)
                )
                losses.append(loss)
        
        if losses:
            return torch.stack(losses).mean()
        else:
            return torch.tensor(0.0, device=embeddings.device)
    
    def _random_triplet_loss(self, embeddings, track_ids):
        """Random sampling triplet loss"""
        losses = []
        
        for track_id in torch.unique(track_ids):
            pos_mask = track_ids == track_id
            pos_embeddings = embeddings[pos_mask]
            
            if len(pos_embeddings) < 2:
                continue
            
            neg_mask = track_ids != track_id
            neg_embeddings = embeddings[neg_mask]
            
            if len(neg_embeddings) == 0:
                continue
            
            # Random sampling
            anchor_idx = torch.randint(0, len(pos_embeddings), (1,))
            anchor = pos_embeddings[anchor_idx]
            
            pos_idx = torch.randint(0, len(pos_embeddings), (1,))
            while pos_idx == anchor_idx and len(pos_embeddings) > 1:
                pos_idx = torch.randint(0, len(pos_embeddings), (1,))
            positive = pos_embeddings[pos_idx]
            
            neg_idx = torch.randint(0, len(neg_embeddings), (1,))
            negative = neg_embeddings[neg_idx]
            
            loss = self.triplet_loss(anchor, positive, negative)
            losses.append(loss)
        
        if losses:
            return torch.stack(losses).mean()
        else:
            return torch.tensor(0.0, device=embeddings.device)


class ContrastiveLoss(nn.Module):
    """Contrastive loss for tracking"""
    
    def __init__(self, margin=1.0, temperature=0.1):
        super(ContrastiveLoss, self).__init__()
        self.margin = margin
        self.temperature = temperature
    
    def forward(self, embeddings, track_ids):
        """
        Args:
            embeddings: Person embeddings [N, D]
            track_ids: Track IDs [N]
        """
        if len(embeddings) < 2:
            return torch.tensor(0.0, device=embeddings.device)
        
        # Normalize embeddings
        embeddings = F.normalize(embeddings, p=2, dim=1)
        
        # Compute similarity matrix
        similarity_matrix = torch.mm(embeddings, embeddings.t()) / self.temperature
        
        # Create labels (1 for same track, 0 for different tracks)
        labels = (track_ids.unsqueeze(0) == track_ids.unsqueeze(1)).float()
        
        # Remove diagonal (self-similarity)
        mask = torch.eye(len(embeddings), device=embeddings.device).bool()
        similarity_matrix = similarity_matrix.masked_fill(mask, -float('inf'))
        labels = labels.masked_fill(mask, 0)
        
        # Compute contrastive loss
        pos_mask = labels == 1
        neg_mask = labels == 0
        
        if pos_mask.sum() == 0 or neg_mask.sum() == 0:
            return torch.tensor(0.0, device=embeddings.device)
        
        # Positive pairs
        pos_sim = similarity_matrix[pos_mask]
        pos_loss = -torch.log(torch.exp(pos_sim) / torch.exp(similarity_matrix).sum(dim=1, keepdim=True).expand_as(similarity_matrix)[pos_mask])
        
        return pos_loss.mean()


class MultiTaskLoss(nn.Module):
    """Combined multi-task loss for pose estimation and tracking"""
    
    def __init__(self, 
                 keypoint_weight=1.0,
                 paf_weight=1.0,
                 instance_weight=0.5,
                 tracking_weight=0.5,
                 contrastive_weight=0.3):
        super(MultiTaskLoss, self).__init__()
        
        self.keypoint_weight = keypoint_weight
        self.paf_weight = paf_weight
        self.instance_weight = instance_weight
        self.tracking_weight = tracking_weight
        self.contrastive_weight = contrastive_weight
        
        self.keypoint_loss = KeypointLoss()
        self.paf_loss = PAFLoss()
        self.instance_loss = InstanceLoss()
        self.tracking_loss = TrackingLoss()
        self.contrastive_loss = ContrastiveLoss()
    
    def forward(self, predictions, targets):
        """
        Args:
            predictions: Model predictions
            targets: Ground truth targets
        """
        losses = {}
        total_loss = 0
        
        # Keypoint loss
        if 'keypoints' in predictions and 'keypoint_targets' in targets:
            kp_loss = self.keypoint_loss(
                predictions['keypoints'], 
                targets['keypoint_targets']
            )
            losses['keypoint_loss'] = kp_loss
            total_loss += self.keypoint_weight * kp_loss
        
        # PAF loss
        if 'pafs' in predictions and 'paf_targets' in targets:
            paf_loss_val = self.paf_loss(
                predictions['pafs'], 
                targets['paf_targets']
            )
            losses['paf_loss'] = paf_loss_val
            total_loss += self.paf_weight * paf_loss_val
        
        # Instance loss
        if 'instances' in predictions and 'instance_targets' in targets:
            inst_loss = self.instance_loss(
                predictions['instances'], 
                targets['instance_targets']
            )
            losses['instance_loss'] = inst_loss
            total_loss += self.instance_weight * inst_loss
        
        # Tracking losses
        if 'embeddings' in predictions and 'track_ids' in targets:
            embeddings = predictions['embeddings']
            track_ids = targets['track_ids']
            
            if len(embeddings) > 0 and len(torch.unique(track_ids)) > 1:
                # Triplet loss
                triplet_loss = self.tracking_loss(embeddings, track_ids)
                losses['triplet_loss'] = triplet_loss
                total_loss += self.tracking_weight * triplet_loss
                
                # Contrastive loss
                contrast_loss = self.contrastive_loss(embeddings, track_ids)
                losses['contrastive_loss'] = contrast_loss
                total_loss += self.contrastive_weight * contrast_loss
        
        losses['total_loss'] = total_loss
        return losses
