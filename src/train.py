import os
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
from tqdm import tqdm
import json
from datetime import datetime

from models.pose_track_model import KeypointPoseTrackModel, KeypointTrackingLoss
from datasets.posetrack_dataset import PoseTrackDataset, collate_fn
from utils.metrics import PoseTrackingMetrics


def parse_args():
    parser = argparse.ArgumentParser(description='Train PoseTrack Model')
    
    # Dataset arguments
    parser.add_argument('--data_root', type=str, required=True,
                       help='Root directory of PoseTrack21 dataset')
    parser.add_argument('--batch_size', type=int, default=8,
                       help='Batch size for training')
    parser.add_argument('--num_workers', type=int, default=4,
                       help='Number of data loading workers')
    parser.add_argument('--image_size', type=int, nargs=2, default=[512, 512],
                       help='Input image size (H, W)')
    parser.add_argument('--heatmap_size', type=int, nargs=2, default=[128, 128],
                       help='Heatmap size (H, W)')
    
    # Model arguments
    parser.add_argument('--num_keypoints', type=int, default=17,
                       help='Number of keypoints')
    parser.add_argument('--embed_dim', type=int, default=128,
                       help='Embedding dimension for tracking')
    parser.add_argument('--backbone_pretrained', action='store_true', default=True,
                       help='Use pretrained backbone')
    
    # Training arguments
    parser.add_argument('--epochs', type=int, default=100,
                       help='Number of training epochs')
    parser.add_argument('--lr', type=float, default=1e-4,
                       help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4,
                       help='Weight decay')
    parser.add_argument('--lr_schedule', type=str, default='cosine',
                       choices=['cosine', 'step', 'plateau'],
                       help='Learning rate schedule')
    parser.add_argument('--warmup_epochs', type=int, default=5,
                       help='Warmup epochs')
    
    # Loss weights
    parser.add_argument('--keypoint_weight', type=float, default=1.0,
                       help='Weight for keypoint loss')
    parser.add_argument('--paf_weight', type=float, default=1.0,
                       help='Weight for PAF loss')
    parser.add_argument('--instance_weight', type=float, default=0.5,
                       help='Weight for instance loss')
    parser.add_argument('--tracking_weight', type=float, default=0.5,
                       help='Weight for tracking loss')
    parser.add_argument('--contrastive_weight', type=float, default=0.3,
                       help='Weight for contrastive loss')
    
    # Checkpoint and logging
    parser.add_argument('--output_dir', type=str, default='./outputs',
                       help='Output directory for checkpoints and logs')
    parser.add_argument('--save_freq', type=int, default=10,
                       help='Save checkpoint frequency (epochs)')
    parser.add_argument('--eval_freq', type=int, default=5,
                       help='Evaluation frequency (epochs)')
    parser.add_argument('--resume', type=str, default=None,
                       help='Resume from checkpoint')
    
    # Device
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use for training')
    parser.add_argument('--mixed_precision', action='store_true',
                       help='Use mixed precision training')
    
    return parser.parse_args()


def create_data_loaders(args):
    """Create training and validation data loaders"""
    
    # Training dataset
    train_dataset = PoseTrackDataset(
        data_root=args.data_root,
        split='train',
        image_size=tuple(args.image_size),
        heatmap_size=tuple(args.heatmap_size),
        sequence_length=3
    )
    
    # Validation dataset
    val_dataset = PoseTrackDataset(
        data_root=args.data_root,
        split='val',
        image_size=tuple(args.image_size),
        heatmap_size=tuple(args.heatmap_size),
        sequence_length=3
    )
    
    # Data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=args.num_workers,
        pin_memory=True,
        drop_last=True,
        collate_fn=collate_fn
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=args.num_workers,
        pin_memory=True,
        collate_fn=collate_fn
    )
    
    return train_loader, val_loader


def create_model_and_optimizer(args):
    """Create model, loss function, and optimizer"""

    # Model
    model = KeypointPoseTrackModel(
        num_keypoints=args.num_keypoints,
        embed_dim=args.embed_dim,
        backbone_pretrained=args.backbone_pretrained,
        max_track_ids=1000
    )

    # Loss function
    criterion = KeypointTrackingLoss(
        heatmap_weight=args.keypoint_weight,
        embedding_weight=args.tracking_weight,
        displacement_weight=0.3,
        association_weight=0.4,
        track_id_weight=0.3,
        visibility_weight=0.2,
        consistency_weight=0.1
    )
    
    # Optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=args.lr,
        weight_decay=args.weight_decay
    )
    
    # Learning rate scheduler
    if args.lr_schedule == 'cosine':
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=args.epochs
        )
    elif args.lr_schedule == 'step':
        scheduler = optim.lr_scheduler.StepLR(
            optimizer, step_size=30, gamma=0.1
        )
    else:  # plateau
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', patience=10
        )
    
    return model, criterion, optimizer, scheduler


def train_epoch(model, train_loader, criterion, optimizer, device, epoch, writer, args):
    """Train for one epoch"""
    model.train()
    
    total_loss_value = 0
    total_samples = 0
    
    # Use mixed precision if specified
    scaler = torch.cuda.amp.GradScaler() if args.mixed_precision else None
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch}')
    
    for batch_idx, batch in enumerate(pbar):
        # Move data to device
        images = batch['images'].to(device)  # [B, T, 3, H, W]
        heatmap_targets = batch['heatmap_targets'].to(device)
        displacement_targets = batch['displacement_targets'].to(device)
        keypoint_track_ids = batch['keypoint_track_ids'].to(device)
        visibility_targets = batch['visibility_targets'].to(device)
        association_targets = batch['association_targets'].to(device)

        batch_size, seq_len = images.shape[:2]

        # Process each frame in sequence
        total_loss = 0
        frame_losses = []

        # Reset model tracking state for new sequence
        model.reset_tracking_state()

        for t in range(seq_len):
            frame_images = images[:, t]  # [B, 3, H, W]
            frame_heatmap_targets = heatmap_targets[:, t]  # [B, K, H, W]
            frame_displacement_targets = displacement_targets[:, t]  # [B, K, 2, H, W]
            frame_track_ids = keypoint_track_ids[:, t]  # [B, K]
            frame_visibility = visibility_targets[:, t]  # [B, K]
            frame_association = association_targets[:, t]  # [B, K]

            optimizer.zero_grad()

            # Forward pass
            if args.mixed_precision:
                with torch.cuda.amp.autocast():
                    outputs = model(frame_images, mode='train')

                    targets = {
                        'heatmap_targets': frame_heatmap_targets,
                        'displacement_targets': frame_displacement_targets,
                        'keypoint_track_ids': frame_track_ids,
                        'track_id_targets': frame_track_ids,  # Same as keypoint_track_ids
                        'visibility_targets': frame_visibility,
                        'association_targets': frame_association
                    }

                    losses = criterion(outputs, targets)
                    loss = losses['total_loss']
            else:
                outputs = model(frame_images, mode='train')

                targets = {
                    'heatmap_targets': frame_heatmap_targets,
                    'displacement_targets': frame_displacement_targets,
                    'keypoint_track_ids': frame_track_ids,
                    'track_id_targets': frame_track_ids,  # Same as keypoint_track_ids
                    'visibility_targets': frame_visibility,
                    'association_targets': frame_association
                }

                losses = criterion(outputs, targets)
                loss = losses['total_loss']

            frame_losses.append(loss)

        # Average loss across frames
        if frame_losses:
            total_loss = torch.stack(frame_losses).mean()
        else:
            continue
        
        # Backward pass
        if args.mixed_precision:
            scaler.scale(total_loss).backward()
            scaler.step(optimizer)
            scaler.update()
        else:
            total_loss.backward()
            optimizer.step()

        # Update metrics
        total_loss_value += total_loss.item()
        total_samples += batch_size
        
        # Update progress bar
        pbar.set_postfix({
            'Loss': f'{total_loss.item():.4f}',
            'Avg Loss': f'{total_loss_value / (batch_idx + 1):.4f}'
        })
        
        # Log to tensorboard
        global_step = epoch * len(train_loader) + batch_idx
        writer.add_scalar('Train/Total_Loss', loss.item(), global_step)
        
        for loss_name, loss_value in losses.items():
            if loss_name != 'total_loss':
                writer.add_scalar(f'Train/{loss_name}', loss_value.item(), global_step)
    
    avg_loss = total_loss_value / len(train_loader)
    return avg_loss


def validate(model, val_loader, criterion, device, epoch, writer, args):
    """Validate the model"""
    model.eval()
    
    total_loss_value = 0
    metrics = PoseTrackingMetrics(num_keypoints=args.num_keypoints)
    
    with torch.no_grad():
        pbar = tqdm(val_loader, desc=f'Validation Epoch {epoch}')
        
        for batch_idx, batch in enumerate(pbar):
            # Move data to device
            images = batch['images'].to(device)
            heatmap_targets = batch['heatmap_targets'].to(device)
            displacement_targets = batch['displacement_targets'].to(device)
            keypoint_track_ids = batch['keypoint_track_ids'].to(device)
            visibility_targets = batch['visibility_targets'].to(device)
            association_targets = batch['association_targets'].to(device)

            batch_size, seq_len = images.shape[:2]

            # Process sequence
            sequence_loss = 0
            frame_count = 0

            # Reset model tracking state
            model.reset_tracking_state()

            for t in range(seq_len):
                frame_images = images[:, t]
                frame_heatmap_targets = heatmap_targets[:, t]
                frame_displacement_targets = displacement_targets[:, t]
                frame_track_ids = keypoint_track_ids[:, t]
                frame_visibility = visibility_targets[:, t]
                frame_association = association_targets[:, t]

                # Forward pass
                outputs = model(frame_images, mode='inference')

                targets = {
                    'heatmap_targets': frame_heatmap_targets,
                    'displacement_targets': frame_displacement_targets,
                    'keypoint_track_ids': frame_track_ids,
                    'track_id_targets': frame_track_ids,
                    'visibility_targets': frame_visibility,
                    'association_targets': frame_association
                }

                losses = criterion(outputs, targets)
                sequence_loss += losses['total_loss'].item()
                frame_count += 1

            if frame_count > 0:
                avg_sequence_loss = sequence_loss / frame_count
                total_loss_value += avg_sequence_loss
            
            # Update progress bar
            pbar.set_postfix({
                'Val Loss': f'{avg_sequence_loss:.4f}' if frame_count > 0 else '0.0000',
                'Avg Val Loss': f'{total_loss_value / (batch_idx + 1):.4f}'
            })

    avg_loss = total_loss_value / len(val_loader)
    
    # Log validation metrics
    writer.add_scalar('Val/Total_Loss', avg_loss, epoch)
    
    # Compute and log metrics
    all_metrics = metrics.compute_all_metrics()
    for metric_name, metric_value in all_metrics.items():
        writer.add_scalar(f'Val/{metric_name}', metric_value, epoch)
    
    return avg_loss, all_metrics


def save_checkpoint(model, optimizer, scheduler, epoch, loss, metrics, output_dir):
    """Save model checkpoint"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'loss': loss,
        'metrics': metrics
    }
    
    checkpoint_path = os.path.join(output_dir, f'checkpoint_epoch_{epoch}.pth')
    torch.save(checkpoint, checkpoint_path)
    
    # Save best model
    best_path = os.path.join(output_dir, 'best_model.pth')
    if not os.path.exists(best_path) or loss < torch.load(best_path)['loss']:
        torch.save(checkpoint, best_path)
    
    print(f'Checkpoint saved: {checkpoint_path}')


def main():
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Save arguments
    with open(os.path.join(args.output_dir, 'args.json'), 'w') as f:
        json.dump(vars(args), f, indent=2)
    
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')
    
    # Create data loaders
    print('Creating data loaders...')
    train_loader, val_loader = create_data_loaders(args)
    print(f'Train samples: {len(train_loader.dataset)}')
    print(f'Val samples: {len(val_loader.dataset)}')
    
    # Create model and optimizer
    print('Creating model and optimizer...')
    model, criterion, optimizer, scheduler = create_model_and_optimizer(args)
    model = model.to(device)
    criterion = criterion.to(device)
    
    # Resume from checkpoint if specified
    start_epoch = 0
    if args.resume:
        print(f'Resuming from checkpoint: {args.resume}')
        checkpoint = torch.load(args.resume, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        start_epoch = checkpoint['epoch'] + 1
    
    # Create tensorboard writer
    log_dir = os.path.join(args.output_dir, 'logs', datetime.now().strftime('%Y%m%d_%H%M%S'))
    writer = SummaryWriter(log_dir)
    
    # Training loop
    print('Starting training...')
    best_val_loss = float('inf')
    
    for epoch in range(start_epoch, args.epochs):
        # Training
        train_loss = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch, writer, args
        )
        
        # Validation
        if epoch % args.eval_freq == 0:
            val_loss, val_metrics = validate(
                model, val_loader, criterion, device, epoch, writer, args
            )
            
            print(f'Epoch {epoch}: Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}')
            print(f'Val Metrics: {val_metrics}')
            
            # Update learning rate scheduler
            if args.lr_schedule == 'plateau':
                scheduler.step(val_loss)
            else:
                scheduler.step()
            
            # Save checkpoint
            if epoch % args.save_freq == 0 or val_loss < best_val_loss:
                save_checkpoint(
                    model, optimizer, scheduler, epoch, val_loss, val_metrics, args.output_dir
                )
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
        else:
            # Update learning rate scheduler
            if args.lr_schedule != 'plateau':
                scheduler.step()
    
    print('Training completed!')
    writer.close()


if __name__ == '__main__':
    main()
