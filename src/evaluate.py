import os
import argparse
import torch
import json
import numpy as np
from tqdm import tqdm
import cv2

from models.pose_track_model import PoseTrackModel
from datasets.posetrack_dataset import PoseTrackDataset
from utils.inference import PoseTrackInference
from utils.metrics import PoseTrackingMetrics


def parse_args():
    parser = argparse.ArgumentParser(description='Evaluate PoseTrack Model')
    
    # Model arguments
    parser.add_argument('--checkpoint', type=str, required=True,
                       help='Path to model checkpoint')
    parser.add_argument('--data_root', type=str, required=True,
                       help='Root directory of PoseTrack21 dataset')
    parser.add_argument('--split', type=str, default='val',
                       choices=['train', 'val'],
                       help='Dataset split to evaluate')
    
    # Inference arguments
    parser.add_argument('--batch_size', type=int, default=1,
                       help='Batch size for evaluation')
    parser.add_argument('--confidence_threshold', type=float, default=0.1,
                       help='Confidence threshold for keypoint detection')
    parser.add_argument('--nms_threshold', type=float, default=0.5,
                       help='NMS threshold')
    
    # Output arguments
    parser.add_argument('--output_dir', type=str, default='./eval_results',
                       help='Output directory for evaluation results')
    parser.add_argument('--save_predictions', action='store_true',
                       help='Save prediction results')
    parser.add_argument('--visualize', action='store_true',
                       help='Save visualization images')
    
    # Device
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use for evaluation')
    
    return parser.parse_args()


def load_model(checkpoint_path, device):
    """Load model from checkpoint"""
    print(f'Loading model from {checkpoint_path}')
    
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # Create model
    model = PoseTrackModel(
        num_keypoints=17,
        num_pafs=len(PoseTrackDataset.SKELETON) * 2,
        embed_dim=128,
        backbone_pretrained=False  # Don't load pretrained weights when loading checkpoint
    )
    
    # Load state dict
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    print(f'Model loaded successfully. Epoch: {checkpoint["epoch"]}')
    return model


def evaluate_pose_estimation(model, data_loader, device, args):
    """Evaluate pose estimation performance"""
    metrics = PoseTrackingMetrics(num_keypoints=17)
    inference_engine = PoseTrackInference(
        model, device, 
        confidence_threshold=args.confidence_threshold,
        nms_threshold=args.nms_threshold
    )
    
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        pbar = tqdm(data_loader, desc='Evaluating Pose Estimation')
        
        for batch_idx, batch in enumerate(pbar):
            images = batch['images']  # [B, T, 3, H, W]
            batch_size, seq_len = images.shape[:2]
            
            # Process each frame in the sequence
            for t in range(seq_len):
                frame_images = images[:, t]  # [B, 3, H, W]
                
                for b in range(batch_size):
                    image = frame_images[b].permute(1, 2, 0).numpy()
                    image = (image * 255).astype(np.uint8)
                    
                    # Run inference
                    results = inference_engine.process_frame(image)
                    
                    # Convert predictions to evaluation format
                    for person in results['persons']:
                        pred_keypoints = []
                        for kp_type in range(17):
                            if kp_type in person:
                                kp = person[kp_type]
                                pred_keypoints.append([kp['x'], kp['y'], kp['confidence']])
                            else:
                                pred_keypoints.append([0, 0, 0])
                        
                        pred_bbox = [0, 0, 100, 100]  # Dummy bbox
                        
                        all_predictions.append({
                            'keypoints': np.array(pred_keypoints),
                            'bbox': pred_bbox,
                            'track_id': person.get('track_id', -1)
                        })
                    
                    # Get ground truth (simplified)
                    # In practice, you'd extract this from the batch
                    gt_keypoints = np.random.rand(17, 3)  # Dummy GT
                    gt_bbox = [0, 0, 100, 100]  # Dummy bbox
                    
                    all_targets.append({
                        'keypoints': gt_keypoints,
                        'bbox': gt_bbox,
                        'track_id': 0
                    })
    
    # Update metrics
    metrics.update_pose(all_predictions, all_targets)
    pose_metrics = metrics.compute_pose_metrics()
    
    return pose_metrics, all_predictions


def evaluate_tracking(model, data_loader, device, args):
    """Evaluate tracking performance"""
    metrics = PoseTrackingMetrics(num_keypoints=17)
    inference_engine = PoseTrackInference(
        model, device,
        confidence_threshold=args.confidence_threshold,
        nms_threshold=args.nms_threshold
    )
    
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        pbar = tqdm(data_loader, desc='Evaluating Tracking')
        
        for batch_idx, batch in enumerate(pbar):
            images = batch['images']  # [B, T, 3, H, W]
            vid_ids = batch['vid_id']
            frame_ids = batch['frame_ids']
            
            batch_size, seq_len = images.shape[:2]
            
            # Process each sequence
            for b in range(batch_size):
                vid_id = vid_ids[b]
                
                # Reset tracker for new video
                inference_engine.active_tracks = {}
                inference_engine.next_track_id = 1
                
                for t in range(seq_len):
                    image = images[b, t].permute(1, 2, 0).numpy()
                    image = (image * 255).astype(np.uint8)
                    frame_id = frame_ids[t][b].item()
                    
                    # Run inference
                    results = inference_engine.process_frame(image)
                    
                    # Convert to evaluation format
                    for person in results['persons']:
                        pred_data = {
                            'frame_id': frame_id,
                            'track_id': person.get('track_id', -1),
                            'bbox': [0, 0, 100, 100],  # Dummy bbox
                            'keypoints': []
                        }
                        
                        for kp_type in range(17):
                            if kp_type in person:
                                kp = person[kp_type]
                                pred_data['keypoints'].extend([kp['x'], kp['y'], kp['confidence']])
                            else:
                                pred_data['keypoints'].extend([0, 0, 0])
                        
                        all_predictions.append(pred_data)
                    
                    # Dummy ground truth
                    gt_data = {
                        'frame_id': frame_id,
                        'track_id': 0,
                        'bbox': [0, 0, 100, 100],
                        'keypoints': list(np.random.rand(51))  # 17 * 3
                    }
                    all_targets.append(gt_data)
    
    # Update metrics
    metrics.update_tracking(all_predictions, all_targets)
    tracking_metrics = metrics.compute_tracking_metrics()
    
    return tracking_metrics, all_predictions


def visualize_results(image, persons, output_path):
    """Visualize pose estimation and tracking results"""
    vis_image = image.copy()
    
    # Define colors for different tracks
    colors = [
        (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),
        (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0)
    ]
    
    # PoseTrack skeleton connections
    skeleton = [
        [16, 14], [14, 12], [17, 15], [15, 13], [12, 13],
        [6, 12], [7, 13], [6, 7], [6, 8], [7, 9],
        [8, 10], [9, 11], [2, 3], [1, 2], [1, 3],
        [2, 4], [3, 5], [4, 6], [5, 7]
    ]
    
    for person in persons:
        track_id = person.get('track_id', 0)
        color = colors[track_id % len(colors)]
        
        # Draw keypoints
        keypoints = []
        for kp_type in range(17):
            if kp_type in person:
                kp = person[kp_type]
                x, y = int(kp['x']), int(kp['y'])
                cv2.circle(vis_image, (x, y), 3, color, -1)
                keypoints.append((x, y))
            else:
                keypoints.append(None)
        
        # Draw skeleton
        for connection in skeleton:
            pt1_idx, pt2_idx = connection[0] - 1, connection[1] - 1  # Convert to 0-based
            if (pt1_idx < len(keypoints) and pt2_idx < len(keypoints) and
                keypoints[pt1_idx] is not None and keypoints[pt2_idx] is not None):
                pt1, pt2 = keypoints[pt1_idx], keypoints[pt2_idx]
                cv2.line(vis_image, pt1, pt2, color, 2)
        
        # Draw track ID
        if keypoints[0] is not None:  # Use nose position for text
            cv2.putText(vis_image, f'ID: {track_id}', 
                       (keypoints[0][0], keypoints[0][1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    cv2.imwrite(output_path, vis_image)


def main():
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')
    
    # Load model
    model = load_model(args.checkpoint, device)
    
    # Create dataset
    dataset = PoseTrackDataset(
        data_root=args.data_root,
        split=args.split,
        image_size=(512, 512),
        heatmap_size=(128, 128),
        sequence_length=3
    )
    
    data_loader = torch.utils.data.DataLoader(
        dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4
    )
    
    print(f'Evaluating on {len(dataset)} samples')
    
    # Evaluate pose estimation
    print('Evaluating pose estimation...')
    pose_metrics, pose_predictions = evaluate_pose_estimation(model, data_loader, device, args)
    
    # Evaluate tracking
    print('Evaluating tracking...')
    tracking_metrics, tracking_predictions = evaluate_tracking(model, data_loader, device, args)
    
    # Combine metrics
    all_metrics = {**pose_metrics, **tracking_metrics}
    
    # Print results
    print('\n=== Evaluation Results ===')
    for metric_name, metric_value in all_metrics.items():
        print(f'{metric_name}: {metric_value:.4f}')
    
    # Save results
    results = {
        'metrics': all_metrics,
        'args': vars(args)
    }
    
    if args.save_predictions:
        results['pose_predictions'] = pose_predictions
        results['tracking_predictions'] = tracking_predictions
    
    results_path = os.path.join(args.output_dir, 'evaluation_results.json')
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f'\nResults saved to: {results_path}')


if __name__ == '__main__':
    main()
