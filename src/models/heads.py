import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class FPN(nn.Module):
    """Feature Pyramid Network for multi-scale feature fusion"""
    
    def __init__(self, in_channels_list, out_channels=256):
        super(FPN, self).__init__()
        self.inner_blocks = nn.ModuleList()
        self.layer_blocks = nn.ModuleList()
        
        for in_channels in in_channels_list:
            inner_block = nn.Conv2d(in_channels, out_channels, 1)
            layer_block = nn.Conv2d(out_channels, out_channels, 3, padding=1)
            self.inner_blocks.append(inner_block)
            self.layer_blocks.append(layer_block)
    
    def forward(self, features):
        """
        Args:
            features: dict with keys ['c2', 'c3', 'c4', 'c5']
        Returns:
            list of feature maps from high to low resolution
        """
        names = ['c2', 'c3', 'c4', 'c5']
        x = [features[name] for name in names]
        
        results = []
        last_inner = self.inner_blocks[-1](x[-1])
        results.append(self.layer_blocks[-1](last_inner))
        
        for idx in range(len(x) - 2, -1, -1):
            inner_lateral = self.inner_blocks[idx](x[idx])
            feat_shape = inner_lateral.shape[-2:]
            inner_top_down = F.interpolate(last_inner, size=feat_shape, mode="nearest")
            last_inner = inner_lateral + inner_top_down
            results.insert(0, self.layer_blocks[idx](last_inner))
        
        return results


class KeypointTrackingHead(nn.Module):
    """Head for keypoint-level heatmap estimation and tracking"""

    def __init__(self, in_channels=256, num_keypoints=17, embed_dim=64, num_frames=3):
        super(KeypointTrackingHead, self).__init__()
        self.num_keypoints = num_keypoints
        self.embed_dim = embed_dim
        self.num_frames = num_frames

        # Shared feature extraction
        self.shared_conv = nn.Sequential(
            nn.Conv2d(in_channels, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True)
        )

        # Keypoint-specific branches for each joint
        self.keypoint_branches = nn.ModuleList()
        for i in range(num_keypoints):
            branch = nn.ModuleDict({
                # Heatmap estimation branch
                'heatmap_conv': nn.Sequential(
                    nn.Conv2d(256, 128, 3, padding=1),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(128, 64, 3, padding=1),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(64, 1, 1)  # Single heatmap for this keypoint
                ),

                # Temporal embedding branch for tracking
                'temporal_conv': nn.Sequential(
                    nn.Conv2d(256, 128, 3, padding=1),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(128, embed_dim, 3, padding=1),
                    nn.ReLU(inplace=True)
                ),

                # Displacement prediction for temporal consistency
                'displacement_conv': nn.Sequential(
                    nn.Conv2d(256, 64, 3, padding=1),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(64, 32, 3, padding=1),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(32, 2, 1)  # x, y displacement
                )
            })
            self.keypoint_branches.append(branch)

        # Simplified spatial consistency (no attention for memory efficiency)
        self.spatial_conv = nn.Conv2d(embed_dim, embed_dim, 3, padding=1)

        # Simplified temporal consistency (no attention for memory efficiency)
        self.temporal_conv = nn.Conv2d(embed_dim * 2, embed_dim, 3, padding=1)

    def forward(self, x, prev_embeddings=None):
        """
        Args:
            x: Feature maps [B, C, H, W]
            prev_embeddings: Previous frame embeddings [B, K, embed_dim, H, W]
        Returns:
            Dictionary with keypoint heatmaps, embeddings, and displacements
        """
        batch_size, _, height, width = x.shape

        # Extract shared features
        shared_feat = self.shared_conv(x)

        # Process each keypoint independently
        keypoint_heatmaps = []
        keypoint_embeddings = []
        keypoint_displacements = []

        for i, branch in enumerate(self.keypoint_branches):
            # Heatmap estimation
            heatmap = branch['heatmap_conv'](shared_feat)
            keypoint_heatmaps.append(heatmap)

            # Temporal embedding
            embedding = branch['temporal_conv'](shared_feat)
            keypoint_embeddings.append(embedding)

            # Displacement prediction
            displacement = branch['displacement_conv'](shared_feat)
            keypoint_displacements.append(displacement)

        # Stack outputs
        heatmaps = torch.cat(keypoint_heatmaps, dim=1)  # [B, K, H, W]
        embeddings = torch.stack(keypoint_embeddings, dim=1)  # [B, K, embed_dim, H, W]
        displacements = torch.stack(keypoint_displacements, dim=1)  # [B, K, 2, H, W]

        # Apply spatial consistency across keypoints
        embeddings_refined = self._apply_spatial_consistency(embeddings)

        # Apply temporal consistency if previous embeddings are available
        if prev_embeddings is not None:
            embeddings_refined = self._apply_temporal_consistency(
                embeddings_refined, prev_embeddings
            )

        return {
            'heatmaps': heatmaps,
            'embeddings': embeddings_refined,
            'displacements': displacements,
            'raw_embeddings': embeddings
        }

    def _apply_spatial_consistency(self, embeddings):
        """Apply spatial consistency across keypoints (simplified)"""
        B, K, C, H, W = embeddings.shape

        # Simple spatial consistency using convolution
        embeddings_reshaped = embeddings.view(B, K*C, H, W)
        spatial_feat = self.spatial_conv(embeddings_reshaped[:, :C])  # Use first C channels

        # Broadcast to all keypoints
        spatial_feat = spatial_feat.unsqueeze(1).expand(-1, K, -1, -1, -1)

        # Residual connection
        return embeddings + 0.1 * spatial_feat

    def _apply_temporal_consistency(self, current_embeddings, prev_embeddings):
        """Apply temporal consistency for tracking (simplified)"""
        B, K, C, H, W = current_embeddings.shape

        # Concatenate current and previous embeddings
        combined = torch.cat([prev_embeddings, current_embeddings], dim=2)  # [B, K, 2*C, H, W]
        combined_reshaped = combined.view(B, K*2*C, H, W)

        # Apply temporal convolution
        temporal_feat = self.temporal_conv(combined_reshaped[:, :2*C])  # Use first 2*C channels
        temporal_feat = temporal_feat.view(B, 1, C, H, W).expand(-1, K, -1, -1, -1)

        # Residual connection
        return current_embeddings + 0.1 * temporal_feat


class KeypointAssociationHead(nn.Module):
    """Head for keypoint-level temporal association and tracking"""

    def __init__(self, embed_dim=64, num_keypoints=17):
        super(KeypointAssociationHead, self).__init__()
        self.embed_dim = embed_dim
        self.num_keypoints = num_keypoints

        # Association network for each keypoint type
        self.association_networks = nn.ModuleList()
        for i in range(num_keypoints):
            network = nn.Sequential(
                nn.Linear(embed_dim * 2, 256),
                nn.ReLU(inplace=True),
                nn.Dropout(0.3),
                nn.Linear(256, 128),
                nn.ReLU(inplace=True),
                nn.Linear(128, 64),
                nn.ReLU(inplace=True),
                nn.Linear(64, 1)  # Removed Sigmoid for BCEWithLogitsLoss
            )
            self.association_networks.append(network)

        # Confidence prediction for each keypoint
        self.confidence_networks = nn.ModuleList()
        for i in range(num_keypoints):
            network = nn.Sequential(
                nn.Linear(embed_dim, 128),
                nn.ReLU(inplace=True),
                nn.Dropout(0.2),
                nn.Linear(128, 64),
                nn.ReLU(inplace=True),
                nn.Linear(64, 1)  # Removed Sigmoid for BCEWithLogitsLoss
            )
            self.confidence_networks.append(network)

        # Global consistency network
        self.consistency_network = nn.Sequential(
            nn.Linear(num_keypoints, 64),
            nn.ReLU(inplace=True),
            nn.Linear(64, 32),
            nn.ReLU(inplace=True),
            nn.Linear(32, 1)  # Removed Sigmoid for BCEWithLogitsLoss
        )

    def forward(self, current_embeddings, prev_embeddings=None):
        """
        Args:
            current_embeddings: Current frame embeddings [B, K, embed_dim, H, W]
            prev_embeddings: Previous frame embeddings [B, K, embed_dim, H, W]
        Returns:
            Association scores and confidence scores for each keypoint
        """
        B, K, C, H, W = current_embeddings.shape

        # Global average pooling to get keypoint-level embeddings
        current_kp_embeddings = F.adaptive_avg_pool2d(
            current_embeddings.reshape(B*K, C, H, W), 1
        ).reshape(B, K, C)  # [B, K, embed_dim]

        # Compute confidence scores for current keypoints
        confidence_scores = []
        for k in range(K):
            conf = self.confidence_networks[k](current_kp_embeddings[:, k])  # [B, 1]
            confidence_scores.append(conf)
        confidence_scores = torch.cat(confidence_scores, dim=1)  # [B, K]

        results = {
            'confidence_scores': confidence_scores,
            'current_embeddings': current_kp_embeddings
        }

        if prev_embeddings is not None:
            # Global average pooling for previous embeddings
            prev_kp_embeddings = F.adaptive_avg_pool2d(
                prev_embeddings.reshape(B*K, C, H, W), 1
            ).reshape(B, K, C)  # [B, K, embed_dim]

            # Compute association scores for each keypoint type
            association_scores = []
            for k in range(K):
                # Concatenate current and previous embeddings
                combined = torch.cat([
                    current_kp_embeddings[:, k],
                    prev_kp_embeddings[:, k]
                ], dim=1)  # [B, embed_dim*2]

                assoc_score = self.association_networks[k](combined)  # [B, 1]
                association_scores.append(assoc_score)

            association_scores = torch.cat(association_scores, dim=1)  # [B, K]

            # Compute global consistency score
            consistency_score = self.consistency_network(association_scores)  # [B, 1]

            results.update({
                'association_scores': association_scores,
                'consistency_score': consistency_score,
                'prev_embeddings': prev_kp_embeddings
            })

        return results

    def compute_keypoint_similarity(self, emb1, emb2, keypoint_type):
        """Compute similarity between two keypoint embeddings"""
        combined = torch.cat([emb1, emb2], dim=-1)
        similarity = self.association_networks[keypoint_type](combined)
        return similarity


class CompleteKeypointTrackingHead(nn.Module):
    """Complete keypoint-level tracking head"""

    def __init__(self, in_channels=256, embed_dim=64, num_keypoints=17, max_track_ids=1000):
        super(CompleteKeypointTrackingHead, self).__init__()
        self.embed_dim = embed_dim
        self.num_keypoints = num_keypoints
        self.max_track_ids = max_track_ids

        # Keypoint tracking head
        self.keypoint_tracking_head = KeypointTrackingHead(
            in_channels=in_channels,
            num_keypoints=num_keypoints,
            embed_dim=embed_dim
        )

        # Association head
        self.association_head = KeypointAssociationHead(
            embed_dim=embed_dim,
            num_keypoints=num_keypoints
        )

        # Track ID prediction for each keypoint
        self.track_id_heads = nn.ModuleList()
        for i in range(num_keypoints):
            head = nn.Sequential(
                nn.Linear(embed_dim, 256),
                nn.ReLU(inplace=True),
                nn.Dropout(0.3),
                nn.Linear(256, 128),
                nn.ReLU(inplace=True),
                nn.Linear(128, max_track_ids)
            )
            self.track_id_heads.append(head)

        # Keypoint visibility prediction
        self.visibility_heads = nn.ModuleList()
        for i in range(num_keypoints):
            head = nn.Sequential(
                nn.Linear(embed_dim, 64),
                nn.ReLU(inplace=True),
                nn.Linear(64, 32),
                nn.ReLU(inplace=True),
                nn.Linear(32, 1)  # Removed Sigmoid for BCEWithLogitsLoss
            )
            self.visibility_heads.append(head)

    def forward(self, features, prev_embeddings=None, prev_heatmaps=None):
        """
        Args:
            features: Feature maps from backbone [B, C, H, W]
            prev_embeddings: Previous frame embeddings [B, K, embed_dim, H, W]
            prev_heatmaps: Previous frame heatmaps [B, K, H, W]
        Returns:
            Dictionary with keypoint tracking results
        """
        # Get keypoint heatmaps and embeddings
        tracking_outputs = self.keypoint_tracking_head(features, prev_embeddings)

        current_heatmaps = tracking_outputs['heatmaps']
        current_embeddings = tracking_outputs['embeddings']
        displacements = tracking_outputs['displacements']

        # Get association scores
        association_outputs = self.association_head(current_embeddings, prev_embeddings)

        # Global average pooling for track ID prediction
        B, K, C, H, W = current_embeddings.shape
        pooled_embeddings = F.adaptive_avg_pool2d(
            current_embeddings.reshape(B*K, C, H, W), 1
        ).reshape(B, K, C)  # [B, K, embed_dim]

        # Predict track IDs for each keypoint
        track_id_logits = []
        visibility_scores = []

        for k in range(K):
            # Track ID prediction
            track_logits = self.track_id_heads[k](pooled_embeddings[:, k])  # [B, max_track_ids]
            track_id_logits.append(track_logits)

            # Visibility prediction
            visibility = self.visibility_heads[k](pooled_embeddings[:, k])  # [B, 1]
            visibility_scores.append(visibility)

        track_id_logits = torch.stack(track_id_logits, dim=1)  # [B, K, max_track_ids]
        visibility_scores = torch.cat(visibility_scores, dim=1)  # [B, K]

        results = {
            'heatmaps': current_heatmaps,
            'embeddings': current_embeddings,
            'displacements': displacements,
            'track_id_logits': track_id_logits,
            'visibility_scores': visibility_scores,
            **association_outputs
        }

        return results

    def extract_keypoint_tracks(self, heatmaps, embeddings, threshold=0.1):
        """Extract keypoint tracks from heatmaps and embeddings"""
        B, K, H, W = heatmaps.shape

        keypoint_tracks = []

        for b in range(B):
            frame_tracks = []

            for k in range(K):
                # Find peaks in heatmap
                heatmap = heatmaps[b, k].cpu().numpy()
                peaks = self._find_peaks_in_heatmap(heatmap, threshold)

                # Extract embeddings at peak locations
                embedding_map = embeddings[b, k]  # [embed_dim, H, W]

                keypoint_detections = []
                for peak in peaks:
                    x, y, confidence = peak
                    embedding = embedding_map[:, y, x].cpu().numpy()

                    keypoint_detections.append({
                        'keypoint_type': k,
                        'x': x,
                        'y': y,
                        'confidence': confidence,
                        'embedding': embedding
                    })

                frame_tracks.append(keypoint_detections)

            keypoint_tracks.append(frame_tracks)

        return keypoint_tracks

    def _find_peaks_in_heatmap(self, heatmap, threshold=0.1, min_distance=3):
        """Find peaks in a single heatmap"""
        from scipy.ndimage import maximum_filter

        # Apply threshold
        heatmap_thresh = heatmap * (heatmap > threshold)

        # Find local maxima
        local_maxima = maximum_filter(heatmap_thresh, size=min_distance) == heatmap_thresh
        local_maxima = local_maxima & (heatmap_thresh > 0)

        # Get peak coordinates
        peaks = []
        y_coords, x_coords = np.where(local_maxima)

        for y, x in zip(y_coords, x_coords):
            confidence = heatmap[y, x]
            peaks.append((x, y, confidence))

        # Sort by confidence
        peaks.sort(key=lambda x: x[2], reverse=True)

        return peaks
