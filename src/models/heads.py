import torch
import torch.nn as nn
import torch.nn.functional as F


class FPN(nn.Module):
    """Feature Pyramid Network for multi-scale feature fusion"""
    
    def __init__(self, in_channels_list, out_channels=256):
        super(FPN, self).__init__()
        self.inner_blocks = nn.ModuleList()
        self.layer_blocks = nn.ModuleList()
        
        for in_channels in in_channels_list:
            inner_block = nn.Conv2d(in_channels, out_channels, 1)
            layer_block = nn.Conv2d(out_channels, out_channels, 3, padding=1)
            self.inner_blocks.append(inner_block)
            self.layer_blocks.append(layer_block)
    
    def forward(self, features):
        """
        Args:
            features: dict with keys ['c2', 'c3', 'c4', 'c5']
        Returns:
            list of feature maps from high to low resolution
        """
        names = ['c2', 'c3', 'c4', 'c5']
        x = [features[name] for name in names]
        
        results = []
        last_inner = self.inner_blocks[-1](x[-1])
        results.append(self.layer_blocks[-1](last_inner))
        
        for idx in range(len(x) - 2, -1, -1):
            inner_lateral = self.inner_blocks[idx](x[idx])
            feat_shape = inner_lateral.shape[-2:]
            inner_top_down = F.interpolate(last_inner, size=feat_shape, mode="nearest")
            last_inner = inner_lateral + inner_top_down
            results.insert(0, self.layer_blocks[idx](last_inner))
        
        return results


class PoseHead(nn.Module):
    """Head for keypoint detection and Part Affinity Fields"""
    
    def __init__(self, in_channels=256, num_keypoints=17, num_pafs=34):
        super(PoseHead, self).__init__()
        self.num_keypoints = num_keypoints
        self.num_pafs = num_pafs
        
        # Shared feature extraction
        self.shared_conv = nn.Sequential(
            nn.Conv2d(in_channels, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True)
        )
        
        # Keypoint heatmap branch
        self.keypoint_conv = nn.Sequential(
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, num_keypoints, 1)
        )
        
        # Part Affinity Field branch
        self.paf_conv = nn.Sequential(
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, num_pafs, 1)
        )
        
        # Person instance segmentation branch
        self.instance_conv = nn.Sequential(
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 128, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 1, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        shared_feat = self.shared_conv(x)
        
        keypoints = self.keypoint_conv(shared_feat)
        pafs = self.paf_conv(shared_feat)
        instances = self.instance_conv(shared_feat)
        
        return {
            'keypoints': keypoints,
            'pafs': pafs,
            'instances': instances
        }


class TemporalHead(nn.Module):
    """Head for temporal feature embedding and tracking"""
    
    def __init__(self, in_channels=256, embed_dim=128, num_frames=3):
        super(TemporalHead, self).__init__()
        self.embed_dim = embed_dim
        self.num_frames = num_frames
        
        # Temporal feature extraction
        self.temporal_conv = nn.Sequential(
            nn.Conv2d(in_channels, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True)
        )
        
        # Global average pooling for person-level features
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        
        # Embedding layer
        self.embedding = nn.Sequential(
            nn.Linear(256, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(512, embed_dim)
        )
        
        # Temporal attention for multi-frame fusion
        self.temporal_attention = nn.MultiheadAttention(
            embed_dim=embed_dim, 
            num_heads=8, 
            dropout=0.1
        )
        
        # Association head for matching
        self.association_head = nn.Sequential(
            nn.Linear(embed_dim * 2, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Linear(128, 1),
            nn.Sigmoid()
        )
    
    def forward(self, features, person_boxes=None):
        """
        Args:
            features: Feature maps from FPN
            person_boxes: Bounding boxes of detected persons [B, N, 4]
        Returns:
            embeddings: Person embeddings for tracking
            associations: Association scores between frames
        """
        batch_size = features.shape[0]
        
        # Extract temporal features
        temp_feat = self.temporal_conv(features)
        
        if person_boxes is not None:
            # ROI pooling for person-specific features
            person_features = self._roi_pool(temp_feat, person_boxes)
            person_features = self.global_pool(person_features)
            person_features = person_features.view(person_features.size(0), -1)
            embeddings = self.embedding(person_features)
        else:
            # Global feature embedding
            global_feat = self.global_pool(temp_feat)
            global_feat = global_feat.view(batch_size, -1)
            embeddings = self.embedding(global_feat)
        
        return embeddings
    
    def _roi_pool(self, features, boxes, output_size=7):
        """Simple ROI pooling implementation"""
        # This is a simplified version - in practice, you'd use torchvision.ops.roi_pool
        pooled_features = []
        for i, box_list in enumerate(boxes):
            feat = features[i]
            for box in box_list:
                x1, y1, x2, y2 = box.int()
                roi_feat = feat[:, y1:y2, x1:x2]
                roi_feat = F.adaptive_avg_pool2d(roi_feat, output_size)
                pooled_features.append(roi_feat)
        
        if pooled_features:
            return torch.stack(pooled_features)
        else:
            return torch.zeros(1, features.shape[1], output_size, output_size, 
                             device=features.device)
    
    def compute_association(self, embeddings1, embeddings2):
        """Compute association scores between two sets of embeddings"""
        associations = []
        for emb1 in embeddings1:
            for emb2 in embeddings2:
                combined = torch.cat([emb1, emb2], dim=-1)
                score = self.association_head(combined)
                associations.append(score)
        
        return torch.stack(associations) if associations else torch.tensor([])


class TrackingHead(nn.Module):
    """Complete tracking head combining pose and temporal information"""
    
    def __init__(self, in_channels=256, embed_dim=128, num_keypoints=17):
        super(TrackingHead, self).__init__()
        self.embed_dim = embed_dim
        self.num_keypoints = num_keypoints
        
        # Pose-aware feature extraction
        self.pose_conv = nn.Sequential(
            nn.Conv2d(in_channels + num_keypoints, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True)
        )
        
        # Temporal embedding
        self.temporal_head = TemporalHead(256, embed_dim)
        
        # Track ID prediction
        self.track_id_head = nn.Sequential(
            nn.Linear(embed_dim, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Linear(128, 1000)  # Max 1000 track IDs
        )
    
    def forward(self, features, keypoint_heatmaps, person_boxes=None):
        """
        Args:
            features: Feature maps from backbone
            keypoint_heatmaps: Keypoint heatmaps from pose head
            person_boxes: Person bounding boxes
        Returns:
            embeddings: Person embeddings for tracking
            track_ids: Predicted track IDs
        """
        # Combine pose and appearance features
        combined_feat = torch.cat([features, keypoint_heatmaps], dim=1)
        pose_feat = self.pose_conv(combined_feat)
        
        # Extract temporal embeddings
        embeddings = self.temporal_head(pose_feat, person_boxes)
        
        # Predict track IDs
        track_ids = self.track_id_head(embeddings)
        
        return {
            'embeddings': embeddings,
            'track_ids': track_ids
        }
