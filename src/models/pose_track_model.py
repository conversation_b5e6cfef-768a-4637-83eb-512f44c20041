import torch
import torch.nn as nn
import torch.nn.functional as F
from .backbone import resnet50_backbone
from .heads import FPN, KeypointTrackingHead, CompleteKeypointTrackingHead


class KeypointPoseTrackModel(nn.Module):
    """Keypoint-level Pose Estimation and Tracking Model"""

    def __init__(self,
                 num_keypoints=17,
                 embed_dim=64,
                 backbone_pretrained=True,
                 max_track_ids=1000):
        super(KeypointPoseTrackModel, self).__init__()

        self.num_keypoints = num_keypoints
        self.embed_dim = embed_dim
        self.max_track_ids = max_track_ids

        # Backbone network
        self.backbone = resnet50_backbone(pretrained=backbone_pretrained)

        # Feature Pyramid Network
        self.fpn = FPN(
            in_channels_list=[256, 512, 1024, 2048],  # ResNet-50 channels
            out_channels=256
        )

        # Keypoint tracking head (combines pose estimation and tracking)
        self.keypoint_tracking_head = CompleteKeypointTrackingHead(
            in_channels=256,
            embed_dim=embed_dim,
            num_keypoints=num_keypoints,
            max_track_ids=max_track_ids
        )

        # Upsampling layers for final heatmap output
        self.upsample = nn.Sequential(
            nn.ConvTranspose2d(num_keypoints, num_keypoints, 4, stride=2, padding=1),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(num_keypoints, num_keypoints, 4, stride=2, padding=1)
        )

        # Store previous frame information for tracking
        self.prev_embeddings = None
        self.prev_heatmaps = None
        
    def forward(self, images, mode='train', reset_tracking=False):
        """
        Args:
            images: Input images [B, 3, H, W]
            mode: 'train' or 'inference'
            reset_tracking: Whether to reset tracking state

        Returns:
            Dictionary containing:
            - heatmaps: Keypoint heatmaps [B, K, H, W]
            - embeddings: Keypoint embeddings [B, K, embed_dim, H, W]
            - displacements: Temporal displacements [B, K, 2, H, W]
            - track_id_logits: Track ID predictions [B, K, max_track_ids]
            - visibility_scores: Keypoint visibility [B, K]
            - association_scores: Temporal association scores [B, K]
            - confidence_scores: Detection confidence [B, K]
        """
        _, _, height, width = images.shape

        # Reset tracking state if requested
        if reset_tracking:
            self.prev_embeddings = None
            self.prev_heatmaps = None

        # Extract multi-scale features
        backbone_features = self.backbone(images)

        # Feature pyramid fusion
        fpn_features = self.fpn(backbone_features)

        # Use the highest resolution feature map
        main_features = fpn_features[0]  # 1/4 scale

        # Keypoint tracking (pose estimation + tracking)
        tracking_outputs = self.keypoint_tracking_head(
            main_features,
            prev_embeddings=self.prev_embeddings,
            prev_heatmaps=self.prev_heatmaps
        )

        # Extract outputs
        keypoint_heatmaps = tracking_outputs['heatmaps']  # [B, K, H/4, W/4]
        embeddings = tracking_outputs['embeddings']
        displacements = tracking_outputs['displacements']
        track_id_logits = tracking_outputs['track_id_logits']
        visibility_scores = tracking_outputs['visibility_scores']

        # Upsample heatmaps to original resolution
        upsampled_heatmaps = self.upsample(keypoint_heatmaps)
        final_heatmaps = F.interpolate(
            upsampled_heatmaps,
            size=(height, width),
            mode='bilinear',
            align_corners=False
        )

        # Prepare outputs
        outputs = {
            'heatmaps': final_heatmaps,
            'low_res_heatmaps': keypoint_heatmaps,
            'embeddings': embeddings,
            'displacements': displacements,
            'track_id_logits': track_id_logits,
            'visibility_scores': visibility_scores
        }

        # Add association outputs if available
        if 'association_scores' in tracking_outputs:
            outputs['association_scores'] = tracking_outputs['association_scores']
        if 'confidence_scores' in tracking_outputs:
            outputs['confidence_scores'] = tracking_outputs['confidence_scores']
        if 'consistency_score' in tracking_outputs:
            outputs['consistency_score'] = tracking_outputs['consistency_score']

        # Update tracking state for next frame
        if mode == 'inference':
            self.prev_embeddings = embeddings.detach()
            self.prev_heatmaps = keypoint_heatmaps.detach()

        return outputs
    
    def extract_keypoint_features(self, images, keypoint_locations=None):
        """Extract features for specific keypoint locations"""
        backbone_features = self.backbone(images)
        fpn_features = self.fpn(backbone_features)
        main_features = fpn_features[0]

        tracking_outputs = self.keypoint_tracking_head(
            main_features,
            prev_embeddings=None,
            prev_heatmaps=None
        )

        embeddings = tracking_outputs['embeddings']  # [B, K, embed_dim, H, W]

        if keypoint_locations is not None:
            # Extract embeddings at specific locations
            extracted_embeddings = []
            for b, locations in enumerate(keypoint_locations):
                batch_embeddings = []
                for k, (x, y) in enumerate(locations):
                    if x >= 0 and y >= 0:  # Valid location
                        emb = embeddings[b, k, :, y, x]
                        batch_embeddings.append(emb)
                    else:
                        # Invalid location, use zero embedding
                        emb = torch.zeros(self.embed_dim, device=embeddings.device)
                        batch_embeddings.append(emb)
                extracted_embeddings.append(torch.stack(batch_embeddings))
            return torch.stack(extracted_embeddings)

        return embeddings

    def compute_keypoint_similarity(self, embeddings1, embeddings2, keypoint_type=None):
        """Compute cosine similarity between keypoint embeddings"""
        if keypoint_type is not None:
            # Compare specific keypoint type
            emb1 = embeddings1[:, keypoint_type]  # [B, embed_dim]
            emb2 = embeddings2[:, keypoint_type]  # [B, embed_dim]
        else:
            # Compare all keypoints
            emb1 = embeddings1.view(-1, self.embed_dim)  # [B*K, embed_dim]
            emb2 = embeddings2.view(-1, self.embed_dim)  # [B*K, embed_dim]

        # Normalize embeddings
        emb1 = F.normalize(emb1, p=2, dim=-1)
        emb2 = F.normalize(emb2, p=2, dim=-1)

        # Compute cosine similarity
        similarity = torch.mm(emb1, emb2.t())
        return similarity

    def get_keypoint_tracks(self, heatmaps, embeddings, threshold=0.1):
        """Extract keypoint tracks from heatmaps and embeddings"""
        return self.keypoint_tracking_head.extract_keypoint_tracks(
            heatmaps, embeddings, threshold
        )

    def reset_tracking_state(self):
        """Reset tracking state for new sequence"""
        self.prev_embeddings = None
        self.prev_heatmaps = None


class KeypointTrackingLoss(nn.Module):
    """Multi-task loss for keypoint-level pose estimation and tracking"""

    def __init__(self,
                 heatmap_weight=1.0,
                 embedding_weight=0.5,
                 displacement_weight=0.3,
                 association_weight=0.4,
                 track_id_weight=0.3,
                 visibility_weight=0.2,
                 consistency_weight=0.1):
        super(KeypointTrackingLoss, self).__init__()

        self.heatmap_weight = heatmap_weight
        self.embedding_weight = embedding_weight
        self.displacement_weight = displacement_weight
        self.association_weight = association_weight
        self.track_id_weight = track_id_weight
        self.visibility_weight = visibility_weight
        self.consistency_weight = consistency_weight

        self.mse_loss = nn.MSELoss()
        self.bce_loss = nn.BCELoss()
        self.ce_loss = nn.CrossEntropyLoss(ignore_index=-1)
        self.l1_loss = nn.L1Loss()
        self.triplet_loss = nn.TripletMarginLoss(margin=0.3)
    
    def forward(self, predictions, targets):
        """
        Args:
            predictions: Model outputs
            targets: Ground truth targets

        Returns:
            Dictionary of losses
        """
        losses = {}
        total_loss = 0

        # Heatmap loss (use low_res_heatmaps to match target size)
        if 'low_res_heatmaps' in predictions and 'heatmap_targets' in targets:
            heatmap_loss = self.mse_loss(
                predictions['low_res_heatmaps'],
                targets['heatmap_targets']
            )
            losses['heatmap_loss'] = heatmap_loss
            total_loss += self.heatmap_weight * heatmap_loss

        # Embedding consistency loss (triplet loss for each keypoint type)
        if 'embeddings' in predictions and 'keypoint_track_ids' in targets:
            embeddings = predictions['embeddings']  # [B, K, embed_dim, H, W]
            track_ids = targets['keypoint_track_ids']  # [B, K, max_detections]

            embedding_loss = self._compute_keypoint_embedding_loss(embeddings, track_ids)
            losses['embedding_loss'] = embedding_loss
            total_loss += self.embedding_weight * embedding_loss

        # Displacement loss
        if 'displacements' in predictions and 'displacement_targets' in targets:
            displacement_loss = self.l1_loss(
                predictions['displacements'],
                targets['displacement_targets']
            )
            losses['displacement_loss'] = displacement_loss
            total_loss += self.displacement_weight * displacement_loss

        # Association loss
        if 'association_scores' in predictions and 'association_targets' in targets:
            association_loss = self.bce_loss(
                predictions['association_scores'],
                targets['association_targets']
            )
            losses['association_loss'] = association_loss
            total_loss += self.association_weight * association_loss

        # Track ID classification loss
        if 'track_id_logits' in predictions and 'track_id_targets' in targets:
            B, K, _ = predictions['track_id_logits'].shape
            track_id_logits = predictions['track_id_logits'].view(-1, predictions['track_id_logits'].size(-1))
            track_id_targets = targets['track_id_targets'].view(-1)

            id_loss = self.ce_loss(track_id_logits, track_id_targets)
            losses['track_id_loss'] = id_loss
            total_loss += self.track_id_weight * id_loss

        # Visibility loss
        if 'visibility_scores' in predictions and 'visibility_targets' in targets:
            visibility_loss = self.bce_loss(
                predictions['visibility_scores'],
                targets['visibility_targets']
            )
            losses['visibility_loss'] = visibility_loss
            total_loss += self.visibility_weight * visibility_loss

        # Consistency loss
        if 'consistency_score' in predictions and 'consistency_target' in targets:
            consistency_loss = self.bce_loss(
                predictions['consistency_score'],
                targets['consistency_target']
            )
            losses['consistency_loss'] = consistency_loss
            total_loss += self.consistency_weight * consistency_loss

        losses['total_loss'] = total_loss
        return losses
    
    def _compute_keypoint_embedding_loss(self, embeddings, track_ids):
        """Compute triplet loss for keypoint embeddings"""
        B, K, C, H, W = embeddings.shape

        # Global average pooling to get keypoint-level embeddings
        keypoint_embeddings = F.adaptive_avg_pool2d(
            embeddings.reshape(B*K, C, H, W), 1
        ).reshape(B, K, C)  # [B, K, embed_dim]

        total_loss = 0
        valid_losses = 0

        # Compute triplet loss for each keypoint type
        for k in range(K):
            kp_embeddings = keypoint_embeddings[:, k]  # [B, embed_dim]
            kp_track_ids = track_ids[:, k] if track_ids.dim() > 1 else track_ids  # [B]

            # Filter out invalid track IDs (-1)
            valid_mask = kp_track_ids >= 0
            if valid_mask.sum() < 2:
                continue

            valid_embeddings = kp_embeddings[valid_mask]
            valid_track_ids = kp_track_ids[valid_mask]

            unique_ids = torch.unique(valid_track_ids)
            if len(unique_ids) < 2:
                continue

            triplet_losses = []

            for track_id in unique_ids:
                # Get positive samples (same track ID)
                pos_mask = valid_track_ids == track_id
                pos_embeddings = valid_embeddings[pos_mask]

                if len(pos_embeddings) < 2:
                    continue

                # Get negative samples (different track IDs)
                neg_mask = valid_track_ids != track_id
                neg_embeddings = valid_embeddings[neg_mask]

                if len(neg_embeddings) == 0:
                    continue

                # Compute triplet loss for this track ID
                anchor = pos_embeddings[0]
                positive = pos_embeddings[1] if len(pos_embeddings) > 1 else pos_embeddings[0]
                negative = neg_embeddings[torch.randint(0, len(neg_embeddings), (1,))]

                triplet_loss = self.triplet_loss(
                    anchor.unsqueeze(0),
                    positive.unsqueeze(0),
                    negative
                )
                triplet_losses.append(triplet_loss)

            if triplet_losses:
                total_loss += torch.stack(triplet_losses).mean()
                valid_losses += 1

        if valid_losses > 0:
            return total_loss / valid_losses
        else:
            return torch.tensor(0.0, device=embeddings.device)
