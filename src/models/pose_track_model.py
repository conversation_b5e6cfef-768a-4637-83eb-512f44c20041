import torch
import torch.nn as nn
import torch.nn.functional as F
from .backbone import resnet50_backbone
from .heads import FPN, PoseHead, TrackingHead


class PoseTrackModel(nn.Module):
    """Bottom-up Pose Estimation and Tracking Model"""
    
    def __init__(self, 
                 num_keypoints=17,
                 num_pafs=34,
                 embed_dim=128,
                 backbone_pretrained=True):
        super(PoseTrackModel, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.num_pafs = num_pafs
        self.embed_dim = embed_dim
        
        # Backbone network
        self.backbone = resnet50_backbone(pretrained=backbone_pretrained)
        
        # Feature Pyramid Network
        self.fpn = FPN(
            in_channels_list=[256, 512, 1024, 2048],  # ResNet-50 channels
            out_channels=256
        )
        
        # Pose estimation head
        self.pose_head = PoseHead(
            in_channels=256,
            num_keypoints=num_keypoints,
            num_pafs=num_pafs
        )
        
        # Tracking head
        self.tracking_head = TrackingHead(
            in_channels=256,
            embed_dim=embed_dim,
            num_keypoints=num_keypoints
        )
        
        # Upsampling layers for final output
        self.upsample = nn.Sequential(
            nn.ConvTranspose2d(256, 128, 4, stride=2, padding=1),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(128, 64, 4, stride=2, padding=1),
            nn.ReLU(inplace=True)
        )
        
        # Final output layers
        self.final_keypoint = nn.Conv2d(64, num_keypoints, 1)
        self.final_paf = nn.Conv2d(64, num_pafs, 1)
        self.final_instance = nn.Conv2d(64, 1, 1)
        
    def forward(self, images, person_boxes=None, mode='train'):
        """
        Args:
            images: Input images [B, 3, H, W]
            person_boxes: Person bounding boxes [B, N, 4] (optional)
            mode: 'train' or 'inference'
        
        Returns:
            Dictionary containing:
            - keypoints: Keypoint heatmaps
            - pafs: Part Affinity Fields
            - instances: Instance segmentation
            - embeddings: Person embeddings for tracking
            - track_ids: Predicted track IDs
        """
        batch_size, _, height, width = images.shape
        
        # Extract multi-scale features
        backbone_features = self.backbone(images)
        
        # Feature pyramid fusion
        fpn_features = self.fpn(backbone_features)
        
        # Use the highest resolution feature map for pose estimation
        main_features = fpn_features[0]  # 1/4 scale
        
        # Pose estimation
        pose_outputs = self.pose_head(main_features)
        keypoint_heatmaps = pose_outputs['keypoints']
        paf_maps = pose_outputs['pafs']
        instance_maps = pose_outputs['instances']
        
        # Upsample to original resolution
        upsampled_features = self.upsample(main_features)
        
        final_keypoints = self.final_keypoint(upsampled_features)
        final_pafs = self.final_paf(upsampled_features)
        final_instances = torch.sigmoid(self.final_instance(upsampled_features))
        
        # Resize to match input resolution
        final_keypoints = F.interpolate(final_keypoints, size=(height, width), mode='bilinear', align_corners=False)
        final_pafs = F.interpolate(final_pafs, size=(height, width), mode='bilinear', align_corners=False)
        final_instances = F.interpolate(final_instances, size=(height, width), mode='bilinear', align_corners=False)
        
        outputs = {
            'keypoints': final_keypoints,
            'pafs': final_pafs,
            'instances': final_instances,
            'keypoint_heatmaps': keypoint_heatmaps,
            'paf_maps': paf_maps,
            'instance_maps': instance_maps
        }
        
        # Tracking (only if person boxes are provided or in training mode)
        if person_boxes is not None or mode == 'train':
            tracking_outputs = self.tracking_head(
                main_features, 
                keypoint_heatmaps, 
                person_boxes
            )
            outputs.update(tracking_outputs)
        
        return outputs
    
    def extract_person_features(self, images, person_boxes):
        """Extract features for specific person instances"""
        backbone_features = self.backbone(images)
        fpn_features = self.fpn(backbone_features)
        main_features = fpn_features[0]
        
        pose_outputs = self.pose_head(main_features)
        keypoint_heatmaps = pose_outputs['keypoints']
        
        tracking_outputs = self.tracking_head(
            main_features, 
            keypoint_heatmaps, 
            person_boxes
        )
        
        return tracking_outputs['embeddings']
    
    def compute_similarity(self, embeddings1, embeddings2):
        """Compute cosine similarity between embeddings"""
        embeddings1 = F.normalize(embeddings1, p=2, dim=-1)
        embeddings2 = F.normalize(embeddings2, p=2, dim=-1)
        
        similarity = torch.mm(embeddings1, embeddings2.t())
        return similarity


class PoseTrackLoss(nn.Module):
    """Multi-task loss for pose estimation and tracking"""
    
    def __init__(self, 
                 keypoint_weight=1.0,
                 paf_weight=1.0,
                 instance_weight=0.5,
                 tracking_weight=0.5,
                 id_weight=0.3):
        super(PoseTrackLoss, self).__init__()
        
        self.keypoint_weight = keypoint_weight
        self.paf_weight = paf_weight
        self.instance_weight = instance_weight
        self.tracking_weight = tracking_weight
        self.id_weight = id_weight
        
        self.mse_loss = nn.MSELoss()
        self.bce_loss = nn.BCELoss()
        self.ce_loss = nn.CrossEntropyLoss()
        self.triplet_loss = nn.TripletMarginLoss(margin=0.3)
    
    def forward(self, predictions, targets):
        """
        Args:
            predictions: Model outputs
            targets: Ground truth targets
        
        Returns:
            Dictionary of losses
        """
        losses = {}
        total_loss = 0
        
        # Keypoint heatmap loss
        if 'keypoints' in predictions and 'keypoint_targets' in targets:
            keypoint_loss = self.mse_loss(
                predictions['keypoints'], 
                targets['keypoint_targets']
            )
            losses['keypoint_loss'] = keypoint_loss
            total_loss += self.keypoint_weight * keypoint_loss
        
        # PAF loss
        if 'pafs' in predictions and 'paf_targets' in targets:
            paf_loss = self.mse_loss(
                predictions['pafs'], 
                targets['paf_targets']
            )
            losses['paf_loss'] = paf_loss
            total_loss += self.paf_weight * paf_loss
        
        # Instance segmentation loss
        if 'instances' in predictions and 'instance_targets' in targets:
            instance_loss = self.bce_loss(
                predictions['instances'], 
                targets['instance_targets']
            )
            losses['instance_loss'] = instance_loss
            total_loss += self.instance_weight * instance_loss
        
        # Tracking embedding loss (triplet loss)
        if 'embeddings' in predictions and 'track_ids' in targets:
            # Implement triplet loss for tracking embeddings
            embeddings = predictions['embeddings']
            track_ids = targets['track_ids']
            
            if len(embeddings) > 0 and len(torch.unique(track_ids)) > 1:
                tracking_loss = self._compute_triplet_loss(embeddings, track_ids)
                losses['tracking_loss'] = tracking_loss
                total_loss += self.tracking_weight * tracking_loss
        
        # Track ID classification loss
        if 'track_ids' in predictions and 'track_id_targets' in targets:
            id_loss = self.ce_loss(
                predictions['track_ids'], 
                targets['track_id_targets']
            )
            losses['id_loss'] = id_loss
            total_loss += self.id_weight * id_loss
        
        losses['total_loss'] = total_loss
        return losses
    
    def _compute_triplet_loss(self, embeddings, track_ids):
        """Compute triplet loss for tracking embeddings"""
        unique_ids = torch.unique(track_ids)
        
        if len(unique_ids) < 2:
            return torch.tensor(0.0, device=embeddings.device)
        
        triplet_losses = []
        
        for track_id in unique_ids:
            # Get positive samples (same track ID)
            pos_mask = track_ids == track_id
            pos_embeddings = embeddings[pos_mask]
            
            if len(pos_embeddings) < 2:
                continue
            
            # Get negative samples (different track IDs)
            neg_mask = track_ids != track_id
            neg_embeddings = embeddings[neg_mask]
            
            if len(neg_embeddings) == 0:
                continue
            
            # Compute triplet loss for this track ID
            anchor = pos_embeddings[0]
            positive = pos_embeddings[1] if len(pos_embeddings) > 1 else pos_embeddings[0]
            negative = neg_embeddings[torch.randint(0, len(neg_embeddings), (1,))]
            
            triplet_loss = self.triplet_loss(
                anchor.unsqueeze(0), 
                positive.unsqueeze(0), 
                negative
            )
            triplet_losses.append(triplet_loss)
        
        if triplet_losses:
            return torch.stack(triplet_losses).mean()
        else:
            return torch.tensor(0.0, device=embeddings.device)
