import torch
import numpy as np
from scipy.optimize import linear_sum_assignment
from collections import defaultdict


class PoseTrackingMetrics:
    """Metrics for pose estimation and tracking evaluation"""
    
    def __init__(self, num_keypoints=17, oks_threshold=0.5, distance_threshold=50):
        self.num_keypoints = num_keypoints
        self.oks_threshold = oks_threshold
        self.distance_threshold = distance_threshold
        
        # COCO-style keypoint sigmas for OKS calculation
        self.sigmas = np.array([
            .26, .25, .25, .35, .35, .79, .79, .72, .72, .62, .62, 1.07, 1.07, .87, .87, .89, .89
        ]) / 10.0
        
        self.reset()
    
    def reset(self):
        """Reset all metrics"""
        self.pose_predictions = []
        self.pose_targets = []
        self.tracking_predictions = []
        self.tracking_targets = []
    
    def update_pose(self, predictions, targets):
        """Update pose estimation metrics"""
        self.pose_predictions.extend(predictions)
        self.pose_targets.extend(targets)
    
    def update_tracking(self, predictions, targets):
        """Update tracking metrics"""
        self.tracking_predictions.extend(predictions)
        self.tracking_targets.extend(targets)
    
    def compute_oks(self, pred_keypoints, gt_keypoints, gt_bbox):
        """Compute Object Keypoint Similarity (OKS)"""
        if len(pred_keypoints) != len(gt_keypoints):
            return 0.0
        
        # Calculate area from bounding box
        area = gt_bbox[2] * gt_bbox[3]  # width * height
        if area == 0:
            return 0.0
        
        # Calculate squared distances
        dx = pred_keypoints[:, 0] - gt_keypoints[:, 0]
        dy = pred_keypoints[:, 1] - gt_keypoints[:, 1]
        d_squared = dx**2 + dy**2
        
        # Visibility mask
        vis_mask = gt_keypoints[:, 2] > 0
        
        if vis_mask.sum() == 0:
            return 0.0
        
        # Calculate OKS
        s = 2 * area
        oks_per_joint = np.exp(-d_squared / (2 * s * self.sigmas**2))
        oks = oks_per_joint[vis_mask].mean()
        
        return oks
    
    def compute_pck(self, pred_keypoints, gt_keypoints, threshold=0.2):
        """Compute Percentage of Correct Keypoints (PCK)"""
        if len(pred_keypoints) != len(gt_keypoints):
            return 0.0
        
        # Calculate distances
        distances = np.sqrt(np.sum((pred_keypoints[:, :2] - gt_keypoints[:, :2])**2, axis=1))
        
        # Visibility mask
        vis_mask = gt_keypoints[:, 2] > 0
        
        if vis_mask.sum() == 0:
            return 0.0
        
        # Calculate head size for normalization
        head_size = self._calculate_head_size(gt_keypoints)
        if head_size == 0:
            return 0.0
        
        # Normalize distances by head size
        normalized_distances = distances / head_size
        
        # Count correct keypoints
        correct = (normalized_distances < threshold) & vis_mask
        pck = correct.sum() / vis_mask.sum()
        
        return pck
    
    def _calculate_head_size(self, keypoints):
        """Calculate head size for PCK normalization"""
        # Use distance between head_top and head_bottom if available
        if len(keypoints) >= 3:
            head_top = keypoints[2]  # head_top
            head_bottom = keypoints[1]  # head_bottom
            
            if head_top[2] > 0 and head_bottom[2] > 0:
                head_size = np.sqrt(np.sum((head_top[:2] - head_bottom[:2])**2))
                return head_size
        
        # Fallback: use distance between shoulders
        if len(keypoints) >= 7:
            left_shoulder = keypoints[5]  # left_shoulder
            right_shoulder = keypoints[6]  # right_shoulder
            
            if left_shoulder[2] > 0 and right_shoulder[2] > 0:
                shoulder_dist = np.sqrt(np.sum((left_shoulder[:2] - right_shoulder[:2])**2))
                return shoulder_dist * 0.6  # Approximate head size
        
        return 60.0  # Default head size
    
    def compute_mota(self, pred_tracks, gt_tracks):
        """Compute Multiple Object Tracking Accuracy (MOTA)"""
        total_gt = 0
        total_fp = 0
        total_fn = 0
        total_idsw = 0
        
        for frame_id in gt_tracks.keys():
            gt_frame = gt_tracks[frame_id]
            pred_frame = pred_tracks.get(frame_id, [])
            
            # Match predictions to ground truth
            matches, fp, fn = self._match_detections(pred_frame, gt_frame)
            
            # Count ID switches
            idsw = self._count_id_switches(matches, frame_id)
            
            total_gt += len(gt_frame)
            total_fp += fp
            total_fn += fn
            total_idsw += idsw
        
        if total_gt == 0:
            return 0.0
        
        mota = 1 - (total_fp + total_fn + total_idsw) / total_gt
        return max(0.0, mota)
    
    def _match_detections(self, pred_dets, gt_dets):
        """Match predicted detections to ground truth using Hungarian algorithm"""
        if len(pred_dets) == 0:
            return [], 0, len(gt_dets)
        
        if len(gt_dets) == 0:
            return [], len(pred_dets), 0
        
        # Calculate distance matrix
        distance_matrix = np.zeros((len(pred_dets), len(gt_dets)))
        
        for i, pred in enumerate(pred_dets):
            for j, gt in enumerate(gt_dets):
                # Use center distance for matching
                pred_center = np.array([pred['bbox'][0] + pred['bbox'][2]/2, 
                                      pred['bbox'][1] + pred['bbox'][3]/2])
                gt_center = np.array([gt['bbox'][0] + gt['bbox'][2]/2, 
                                    gt['bbox'][1] + gt['bbox'][3]/2])
                
                distance_matrix[i, j] = np.linalg.norm(pred_center - gt_center)
        
        # Hungarian matching
        pred_indices, gt_indices = linear_sum_assignment(distance_matrix)
        
        matches = []
        for pred_idx, gt_idx in zip(pred_indices, gt_indices):
            if distance_matrix[pred_idx, gt_idx] < self.distance_threshold:
                matches.append((pred_idx, gt_idx))
        
        fp = len(pred_dets) - len(matches)
        fn = len(gt_dets) - len(matches)
        
        return matches, fp, fn
    
    def _count_id_switches(self, matches, frame_id):
        """Count ID switches (simplified implementation)"""
        # This is a simplified version - full implementation would track
        # ID consistency across frames
        return 0
    
    def compute_idf1(self, pred_tracks, gt_tracks):
        """Compute IDF1 score"""
        # Simplified IDF1 calculation
        total_tp = 0
        total_fp = 0
        total_fn = 0
        
        for frame_id in gt_tracks.keys():
            gt_frame = gt_tracks[frame_id]
            pred_frame = pred_tracks.get(frame_id, [])
            
            matches, fp, fn = self._match_detections(pred_frame, gt_frame)
            
            total_tp += len(matches)
            total_fp += fp
            total_fn += fn
        
        if total_tp + total_fp == 0 or total_tp + total_fn == 0:
            return 0.0
        
        precision = total_tp / (total_tp + total_fp)
        recall = total_tp / (total_tp + total_fn)
        
        if precision + recall == 0:
            return 0.0
        
        idf1 = 2 * precision * recall / (precision + recall)
        return idf1
    
    def compute_pose_metrics(self):
        """Compute pose estimation metrics"""
        if not self.pose_predictions or not self.pose_targets:
            return {}
        
        total_oks = 0
        total_pck = 0
        valid_samples = 0
        
        for pred, target in zip(self.pose_predictions, self.pose_targets):
            if 'keypoints' in pred and 'keypoints' in target and 'bbox' in target:
                oks = self.compute_oks(pred['keypoints'], target['keypoints'], target['bbox'])
                pck = self.compute_pck(pred['keypoints'], target['keypoints'])
                
                total_oks += oks
                total_pck += pck
                valid_samples += 1
        
        if valid_samples == 0:
            return {}
        
        return {
            'mAP': total_oks / valid_samples,
            'PCK': total_pck / valid_samples,
            'valid_samples': valid_samples
        }
    
    def compute_tracking_metrics(self):
        """Compute tracking metrics"""
        if not self.tracking_predictions or not self.tracking_targets:
            return {}
        
        # Organize tracks by frame
        pred_tracks = defaultdict(list)
        gt_tracks = defaultdict(list)
        
        for pred in self.tracking_predictions:
            frame_id = pred.get('frame_id', 0)
            pred_tracks[frame_id].append(pred)
        
        for target in self.tracking_targets:
            frame_id = target.get('frame_id', 0)
            gt_tracks[frame_id].append(target)
        
        mota = self.compute_mota(pred_tracks, gt_tracks)
        idf1 = self.compute_idf1(pred_tracks, gt_tracks)
        
        return {
            'MOTA': mota,
            'IDF1': idf1
        }
    
    def compute_all_metrics(self):
        """Compute all metrics"""
        pose_metrics = self.compute_pose_metrics()
        tracking_metrics = self.compute_tracking_metrics()
        
        return {
            **pose_metrics,
            **tracking_metrics
        }
