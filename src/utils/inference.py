import torch
import torch.nn.functional as F
import numpy as np
import cv2
from scipy.ndimage import maximum_filter
from scipy.spatial.distance import cdist
from collections import defaultdict


class PoseTrackInference:
    """Inference pipeline for pose estimation and tracking"""
    
    def __init__(self, model, device='cuda', confidence_threshold=0.1, nms_threshold=0.5):
        self.model = model
        self.device = device
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = nms_threshold
        
        # PoseTrack keypoint connections
        self.skeleton = [
            [16, 14], [14, 12], [17, 15], [15, 13], [12, 13],
            [6, 12], [7, 13], [6, 7], [6, 8], [7, 9],
            [8, 10], [9, 11], [2, 3], [1, 2], [1, 3],
            [2, 4], [3, 5], [4, 6], [5, 7]
        ]
        
        # Track management
        self.active_tracks = {}
        self.next_track_id = 1
        self.max_disappeared = 10
    
    def preprocess_image(self, image, target_size=(512, 512)):
        """Preprocess image for inference"""
        # Resize image
        h, w = image.shape[:2]
        image_resized = cv2.resize(image, target_size)
        
        # Normalize
        image_normalized = image_resized.astype(np.float32) / 255.0
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        image_normalized = (image_normalized - mean) / std
        
        # Convert to tensor
        image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1).unsqueeze(0)
        
        # Scale factors for coordinate conversion
        scale_x = w / target_size[1]
        scale_y = h / target_size[0]
        
        return image_tensor, (scale_x, scale_y)
    
    def extract_keypoints_from_heatmaps(self, heatmaps, scale_factors):
        """Extract keypoint coordinates from heatmaps"""
        keypoints = []
        
        for i in range(heatmaps.shape[1]):  # For each keypoint type
            heatmap = heatmaps[0, i].cpu().numpy()
            
            # Find peaks
            peaks = self._find_peaks(heatmap, threshold=self.confidence_threshold)
            
            # Convert to original image coordinates
            for peak in peaks:
                x, y, confidence = peak
                x_orig = x * scale_factors[0]
                y_orig = y * scale_factors[1]
                keypoints.append({
                    'type': i,
                    'x': x_orig,
                    'y': y_orig,
                    'confidence': confidence
                })
        
        return keypoints
    
    def _find_peaks(self, heatmap, threshold=0.1, min_distance=5):
        """Find peaks in heatmap"""
        # Apply threshold
        heatmap_thresh = heatmap * (heatmap > threshold)
        
        # Find local maxima
        local_maxima = maximum_filter(heatmap_thresh, size=min_distance) == heatmap_thresh
        local_maxima = local_maxima & (heatmap_thresh > 0)
        
        # Get peak coordinates
        peaks = []
        y_coords, x_coords = np.where(local_maxima)
        
        for y, x in zip(y_coords, x_coords):
            confidence = heatmap[y, x]
            peaks.append((x, y, confidence))
        
        # Sort by confidence
        peaks.sort(key=lambda x: x[2], reverse=True)
        
        return peaks
    
    def group_keypoints_to_persons(self, keypoints, paf_maps, scale_factors):
        """Group keypoints into person instances using PAF"""
        if not keypoints:
            return []
        
        # Group keypoints by type
        keypoints_by_type = defaultdict(list)
        for kp in keypoints:
            keypoints_by_type[kp['type']].append(kp)
        
        # Build person instances using PAF connections
        persons = []
        used_keypoints = set()
        
        # Start with high-confidence keypoints
        all_keypoints = sorted(keypoints, key=lambda x: x['confidence'], reverse=True)
        
        for start_kp in all_keypoints:
            if id(start_kp) in used_keypoints:
                continue
            
            # Start a new person
            person = {start_kp['type']: start_kp}
            used_keypoints.add(id(start_kp))
            
            # Grow the person using PAF connections
            self._grow_person_with_paf(person, keypoints_by_type, paf_maps, 
                                     scale_factors, used_keypoints)
            
            # Only keep persons with sufficient keypoints
            if len(person) >= 3:
                persons.append(person)
        
        return persons
    
    def _grow_person_with_paf(self, person, keypoints_by_type, paf_maps, 
                             scale_factors, used_keypoints):
        """Grow person instance using PAF connections"""
        changed = True
        
        while changed:
            changed = False
            
            for connection in self.skeleton:
                joint1_type, joint2_type = connection[0] - 1, connection[1] - 1  # Convert to 0-based
                
                # Check if we have one joint and can find the other
                if joint1_type in person and joint2_type not in person:
                    # Try to find joint2 connected to joint1
                    joint1 = person[joint1_type]
                    candidates = [kp for kp in keypoints_by_type[joint2_type] 
                                if id(kp) not in used_keypoints]
                    
                    best_candidate = self._find_best_connection(
                        joint1, candidates, paf_maps, connection, scale_factors
                    )
                    
                    if best_candidate:
                        person[joint2_type] = best_candidate
                        used_keypoints.add(id(best_candidate))
                        changed = True
                
                elif joint2_type in person and joint1_type not in person:
                    # Try to find joint1 connected to joint2
                    joint2 = person[joint2_type]
                    candidates = [kp for kp in keypoints_by_type[joint1_type] 
                                if id(kp) not in used_keypoints]
                    
                    best_candidate = self._find_best_connection(
                        joint2, candidates, paf_maps, connection, scale_factors
                    )
                    
                    if best_candidate:
                        person[joint1_type] = best_candidate
                        used_keypoints.add(id(best_candidate))
                        changed = True
    
    def _find_best_connection(self, joint, candidates, paf_maps, connection, scale_factors):
        """Find best connection using PAF"""
        if not candidates:
            return None
        
        # Get PAF for this connection
        connection_idx = self.skeleton.index(connection)
        paf_x = paf_maps[0, connection_idx * 2].cpu().numpy()
        paf_y = paf_maps[0, connection_idx * 2 + 1].cpu().numpy()
        
        best_score = -1
        best_candidate = None
        
        for candidate in candidates:
            # Calculate PAF score along the line between joints
            score = self._calculate_paf_score(
                joint, candidate, paf_x, paf_y, scale_factors
            )
            
            if score > best_score and score > 0.1:  # Threshold for PAF score
                best_score = score
                best_candidate = candidate
        
        return best_candidate
    
    def _calculate_paf_score(self, joint1, joint2, paf_x, paf_y, scale_factors):
        """Calculate PAF score between two joints"""
        # Convert coordinates to heatmap space
        x1 = joint1['x'] / scale_factors[0]
        y1 = joint1['y'] / scale_factors[1]
        x2 = joint2['x'] / scale_factors[0]
        y2 = joint2['y'] / scale_factors[1]
        
        # Sample points along the line
        num_samples = 10
        scores = []
        
        for i in range(num_samples):
            t = i / (num_samples - 1)
            x = int(x1 + t * (x2 - x1))
            y = int(y1 + t * (y2 - y1))
            
            if 0 <= x < paf_x.shape[1] and 0 <= y < paf_x.shape[0]:
                # Expected direction
                dx = x2 - x1
                dy = y2 - y1
                norm = np.sqrt(dx**2 + dy**2)
                
                if norm > 0:
                    dx /= norm
                    dy /= norm
                    
                    # PAF direction at this point
                    paf_dx = paf_x[y, x]
                    paf_dy = paf_y[y, x]
                    
                    # Dot product (cosine similarity)
                    score = dx * paf_dx + dy * paf_dy
                    scores.append(score)
        
        return np.mean(scores) if scores else 0
    
    def track_persons(self, current_persons, frame_embeddings=None):
        """Track persons across frames"""
        if not current_persons:
            return []
        
        tracked_persons = []
        
        # Extract features for current persons
        if frame_embeddings is not None:
            current_features = frame_embeddings
        else:
            current_features = [self._extract_person_features(person) for person in current_persons]
        
        # Match with existing tracks
        if self.active_tracks:
            matches = self._match_persons_to_tracks(current_persons, current_features)
            
            # Update matched tracks
            for person_idx, track_id in matches.items():
                person = current_persons[person_idx]
                person['track_id'] = track_id
                self.active_tracks[track_id]['person'] = person
                self.active_tracks[track_id]['disappeared'] = 0
                tracked_persons.append(person)
            
            # Create new tracks for unmatched persons
            unmatched_persons = [i for i in range(len(current_persons)) if i not in matches]
            for person_idx in unmatched_persons:
                person = current_persons[person_idx]
                track_id = self.next_track_id
                self.next_track_id += 1
                
                person['track_id'] = track_id
                self.active_tracks[track_id] = {
                    'person': person,
                    'features': current_features[person_idx] if current_features else None,
                    'disappeared': 0
                }
                tracked_persons.append(person)
            
            # Update disappeared counter for unmatched tracks
            matched_track_ids = set(matches.values())
            for track_id in list(self.active_tracks.keys()):
                if track_id not in matched_track_ids:
                    self.active_tracks[track_id]['disappeared'] += 1
                    
                    # Remove tracks that have disappeared for too long
                    if self.active_tracks[track_id]['disappeared'] > self.max_disappeared:
                        del self.active_tracks[track_id]
        
        else:
            # First frame - create new tracks for all persons
            for i, person in enumerate(current_persons):
                track_id = self.next_track_id
                self.next_track_id += 1
                
                person['track_id'] = track_id
                self.active_tracks[track_id] = {
                    'person': person,
                    'features': current_features[i] if current_features else None,
                    'disappeared': 0
                }
                tracked_persons.append(person)
        
        return tracked_persons
    
    def _extract_person_features(self, person):
        """Extract simple features from person keypoints"""
        # Simple feature: center of mass and bounding box
        keypoints = list(person.values())
        if not keypoints:
            return np.zeros(4)
        
        xs = [kp['x'] for kp in keypoints]
        ys = [kp['y'] for kp in keypoints]
        
        center_x = np.mean(xs)
        center_y = np.mean(ys)
        width = max(xs) - min(xs)
        height = max(ys) - min(ys)
        
        return np.array([center_x, center_y, width, height])
    
    def _match_persons_to_tracks(self, current_persons, current_features):
        """Match current persons to existing tracks"""
        if not self.active_tracks or not current_persons:
            return {}
        
        # Extract features from active tracks
        track_ids = list(self.active_tracks.keys())
        track_features = []
        
        for track_id in track_ids:
            if self.active_tracks[track_id]['features'] is not None:
                track_features.append(self.active_tracks[track_id]['features'])
            else:
                # Fallback to person features
                track_features.append(
                    self._extract_person_features(self.active_tracks[track_id]['person'])
                )
        
        track_features = np.array(track_features)
        current_features = np.array(current_features)
        
        # Calculate distance matrix
        distances = cdist(current_features, track_features, metric='euclidean')
        
        # Hungarian matching (simplified)
        matches = {}
        used_tracks = set()
        
        # Greedy matching for simplicity
        for person_idx in range(len(current_persons)):
            best_track_idx = None
            best_distance = float('inf')
            
            for track_idx in range(len(track_ids)):
                if track_idx in used_tracks:
                    continue
                
                distance = distances[person_idx, track_idx]
                if distance < best_distance and distance < 100:  # Distance threshold
                    best_distance = distance
                    best_track_idx = track_idx
            
            if best_track_idx is not None:
                matches[person_idx] = track_ids[best_track_idx]
                used_tracks.add(best_track_idx)
        
        return matches
    
    def process_frame(self, image):
        """Process a single frame for pose estimation and tracking"""
        self.model.eval()
        
        with torch.no_grad():
            # Preprocess image
            image_tensor, scale_factors = self.preprocess_image(image)
            image_tensor = image_tensor.to(self.device)
            
            # Forward pass
            outputs = self.model(image_tensor, mode='inference')
            
            # Extract keypoints
            keypoints = self.extract_keypoints_from_heatmaps(
                outputs['keypoints'], scale_factors
            )
            
            # Group keypoints into persons
            persons = self.group_keypoints_to_persons(
                keypoints, outputs['pafs'], scale_factors
            )
            
            # Track persons
            tracked_persons = self.track_persons(persons)
            
            return {
                'persons': tracked_persons,
                'keypoints': keypoints,
                'raw_outputs': outputs
            }
