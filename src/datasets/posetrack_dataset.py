import os
import json
import cv2
import numpy as np
import torch
from torch.utils.data import Dataset
from torchvision import transforms
import random
from collections import defaultdict


class PoseTrackDataset(Dataset):
    """PoseTrack21 Dataset for pose estimation and tracking"""
    
    # PoseTrack21 keypoint names
    KEYPOINT_NAMES = [
        'nose', 'head_bottom', 'head_top', 'left_ear', 'right_ear',
        'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
        'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
        'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
    ]
    
    # Skeleton connections for PAF
    SKELETON = [
        [16, 14], [14, 12], [17, 15], [15, 13], [12, 13],
        [6, 12], [7, 13], [6, 7], [6, 8], [7, 9],
        [8, 10], [9, 11], [2, 3], [1, 2], [1, 3],
        [2, 4], [3, 5], [4, 6], [5, 7]
    ]
    
    def __init__(self, 
                 data_root,
                 split='train',
                 image_size=(512, 512),
                 heatmap_size=(128, 128),
                 sigma=2,
                 sequence_length=3,
                 transform=None):
        """
        Args:
            data_root: Root directory of PoseTrack21 dataset
            split: 'train' or 'val'
            image_size: Target image size (H, W)
            heatmap_size: Target heatmap size (H, W)
            sigma: Gaussian sigma for heatmap generation
            sequence_length: Number of frames in a sequence
            transform: Additional image transforms
        """
        self.data_root = data_root
        self.split = split
        self.image_size = image_size
        self.heatmap_size = heatmap_size
        self.sigma = sigma
        self.sequence_length = sequence_length
        self.num_keypoints = len(self.KEYPOINT_NAMES)
        self.num_pafs = len(self.SKELETON) * 2  # x and y components
        
        # Load annotations
        self.annotations = self._load_annotations()
        self.sequences = self._organize_sequences()
        
        # Default transforms
        if transform is None:
            self.transform = transforms.Compose([
                transforms.ToPILImage(),
                transforms.Resize(image_size),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
        else:
            self.transform = transform
    
    def _load_annotations(self):
        """Load PoseTrack annotations"""
        ann_dir = os.path.join(self.data_root, 'posetrack_data', self.split)
        annotations = {}
        
        for ann_file in os.listdir(ann_dir):
            if ann_file.endswith('.json'):
                ann_path = os.path.join(ann_dir, ann_file)
                with open(ann_path, 'r') as f:
                    data = json.load(f)
                
                vid_id = ann_file.split('_')[0]
                annotations[vid_id] = data
        
        return annotations
    
    def _organize_sequences(self):
        """Organize annotations into sequences"""
        sequences = []
        
        for vid_id, ann_data in self.annotations.items():
            # Group images by video
            images = {img['frame_id']: img for img in ann_data['images']}
            annotations = defaultdict(list)
            
            # Group annotations by frame
            for ann in ann_data['annotations']:
                frame_id = ann['image_id']
                annotations[frame_id].append(ann)
            
            # Create sequences
            frame_ids = sorted(images.keys())
            for i in range(0, len(frame_ids) - self.sequence_length + 1, self.sequence_length):
                seq_frames = frame_ids[i:i + self.sequence_length]
                
                # Check if all frames have annotations
                valid_sequence = True
                for frame_id in seq_frames:
                    if frame_id not in annotations or len(annotations[frame_id]) == 0:
                        valid_sequence = False
                        break
                
                if valid_sequence:
                    sequences.append({
                        'vid_id': vid_id,
                        'frame_ids': seq_frames,
                        'images': images,
                        'annotations': annotations
                    })
        
        return sequences
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        
        images = []
        keypoint_targets = []
        paf_targets = []
        instance_targets = []
        track_ids = []
        person_boxes = []
        
        for frame_id in sequence['frame_ids']:
            # Load image
            img_info = sequence['images'][frame_id]
            img_path = os.path.join(self.data_root, img_info['file_name'])
            image = cv2.imread(img_path)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Get annotations for this frame
            frame_annotations = sequence['annotations'][frame_id]
            
            # Generate targets
            keypoint_hm, paf_map, instance_map, boxes, track_id_list = self._generate_targets(
                image, frame_annotations
            )
            
            # Apply transforms
            if self.transform:
                image = self.transform(image)
            
            images.append(image)
            keypoint_targets.append(keypoint_hm)
            paf_targets.append(paf_map)
            instance_targets.append(instance_map)
            person_boxes.append(boxes)
            track_ids.extend(track_id_list)
        
        return {
            'images': torch.stack(images),
            'keypoint_targets': torch.stack(keypoint_targets),
            'paf_targets': torch.stack(paf_targets),
            'instance_targets': torch.stack(instance_targets),
            'person_boxes': person_boxes,
            'track_ids': torch.tensor(track_ids, dtype=torch.long),
            'vid_id': sequence['vid_id'],
            'frame_ids': sequence['frame_ids']
        }
    
    def _generate_targets(self, image, annotations):
        """Generate training targets from annotations"""
        h, w = image.shape[:2]
        hm_h, hm_w = self.heatmap_size
        
        # Initialize target maps
        keypoint_heatmap = np.zeros((self.num_keypoints, hm_h, hm_w), dtype=np.float32)
        paf_map = np.zeros((self.num_pafs, hm_h, hm_w), dtype=np.float32)
        instance_map = np.zeros((1, hm_h, hm_w), dtype=np.float32)
        
        person_boxes = []
        track_ids = []
        
        for ann in annotations:
            if 'keypoints' not in ann or 'bbox' not in ann:
                continue
            
            # Get keypoints and bbox
            keypoints = np.array(ann['keypoints']).reshape(-1, 3)
            bbox = ann['bbox']  # [x, y, w, h]
            track_id = ann.get('track_id', 0)
            
            # Scale coordinates to heatmap size
            scale_x = hm_w / w
            scale_y = hm_h / h
            
            scaled_keypoints = keypoints.copy()
            scaled_keypoints[:, 0] *= scale_x
            scaled_keypoints[:, 1] *= scale_y
            
            # Generate keypoint heatmaps
            for i, (x, y, v) in enumerate(scaled_keypoints):
                if v > 0:  # visible keypoint
                    self._generate_gaussian_heatmap(
                        keypoint_heatmap[i], x, y, self.sigma
                    )
            
            # Generate PAF maps
            self._generate_paf_maps(paf_map, scaled_keypoints, scale_x, scale_y)
            
            # Generate instance map
            x, y, w_box, h_box = bbox
            x1, y1 = int(x * scale_x), int(y * scale_y)
            x2, y2 = int((x + w_box) * scale_x), int((y + h_box) * scale_y)
            instance_map[0, y1:y2, x1:x2] = 1.0
            
            # Store person info
            person_boxes.append([x, y, x + w_box, y + h_box])
            track_ids.append(track_id)
        
        return (
            torch.from_numpy(keypoint_heatmap),
            torch.from_numpy(paf_map),
            torch.from_numpy(instance_map),
            torch.tensor(person_boxes, dtype=torch.float32),
            track_ids
        )
    
    def _generate_gaussian_heatmap(self, heatmap, center_x, center_y, sigma):
        """Generate Gaussian heatmap for a keypoint"""
        h, w = heatmap.shape
        x = np.arange(0, w, 1, np.float32)
        y = np.arange(0, h, 1, np.float32)
        y = y[:, np.newaxis]
        
        # Generate Gaussian
        gaussian = np.exp(-((x - center_x) ** 2 + (y - center_y) ** 2) / (2 * sigma ** 2))
        
        # Add to heatmap (take maximum to handle overlapping)
        heatmap[:] = np.maximum(heatmap, gaussian)
    
    def _generate_paf_maps(self, paf_map, keypoints, scale_x, scale_y):
        """Generate Part Affinity Field maps"""
        h, w = paf_map.shape[1:]
        
        for i, (joint1_idx, joint2_idx) in enumerate(self.SKELETON):
            # Convert to 0-based indexing
            joint1_idx -= 1
            joint2_idx -= 1
            
            if (joint1_idx < len(keypoints) and joint2_idx < len(keypoints) and
                keypoints[joint1_idx][2] > 0 and keypoints[joint2_idx][2] > 0):
                
                x1, y1 = keypoints[joint1_idx][:2]
                x2, y2 = keypoints[joint2_idx][:2]
                
                # Generate PAF vector field
                self._generate_paf_vector(
                    paf_map[i*2:i*2+2], x1, y1, x2, y2, w, h
                )
    
    def _generate_paf_vector(self, paf_channels, x1, y1, x2, y2, w, h):
        """Generate PAF vector field between two keypoints"""
        # Vector from joint1 to joint2
        vec_x = x2 - x1
        vec_y = y2 - y1
        norm = np.sqrt(vec_x**2 + vec_y**2)
        
        if norm == 0:
            return
        
        vec_x /= norm
        vec_y /= norm
        
        # Create coordinate grids
        x_grid, y_grid = np.meshgrid(np.arange(w), np.arange(h))
        
        # Distance from each pixel to the line segment
        dist_to_line = self._point_to_line_distance(
            x_grid, y_grid, x1, y1, x2, y2
        )
        
        # Threshold for PAF width
        threshold = 1.0
        mask = dist_to_line <= threshold
        
        # Set PAF values
        paf_channels[0][mask] = vec_x
        paf_channels[1][mask] = vec_y
    
    def _point_to_line_distance(self, px, py, x1, y1, x2, y2):
        """Calculate distance from points to line segment"""
        # Vector from point1 to point2
        line_vec_x = x2 - x1
        line_vec_y = y2 - y1
        
        # Vector from point1 to each pixel
        point_vec_x = px - x1
        point_vec_y = py - y1
        
        # Project point vector onto line vector
        line_len_sq = line_vec_x**2 + line_vec_y**2
        if line_len_sq == 0:
            return np.sqrt(point_vec_x**2 + point_vec_y**2)
        
        t = np.clip((point_vec_x * line_vec_x + point_vec_y * line_vec_y) / line_len_sq, 0, 1)
        
        # Closest point on line segment
        closest_x = x1 + t * line_vec_x
        closest_y = y1 + t * line_vec_y
        
        # Distance to closest point
        dist = np.sqrt((px - closest_x)**2 + (py - closest_y)**2)
        return dist
