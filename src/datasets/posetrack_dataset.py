import os
import json
import cv2
import numpy as np
import torch
from torch.utils.data import Dataset
from torchvision import transforms
import random
from collections import defaultdict


class PoseTrackDataset(Dataset):
    """PoseTrack21 Dataset for pose estimation and tracking"""
    
    # PoseTrack21 keypoint names
    KEYPOINT_NAMES = [
        'nose', 'head_bottom', 'head_top', 'left_ear', 'right_ear',
        'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
        'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
        'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
    ]
    
    # Skeleton connections for PAF
    SKELETON = [
        [16, 14], [14, 12], [17, 15], [15, 13], [12, 13],
        [6, 12], [7, 13], [6, 7], [6, 8], [7, 9],
        [8, 10], [9, 11], [2, 3], [1, 2], [1, 3],
        [2, 4], [3, 5], [4, 6], [5, 7]
    ]
    
    def __init__(self, 
                 data_root,
                 split='train',
                 image_size=(512, 512),
                 heatmap_size=(128, 128),
                 sigma=2,
                 sequence_length=3,
                 transform=None):
        """
        Args:
            data_root: Root directory of PoseTrack21 dataset
            split: 'train' or 'val'
            image_size: Target image size (H, W)
            heatmap_size: Target heatmap size (H, W)
            sigma: Gaussian sigma for heatmap generation
            sequence_length: Number of frames in a sequence
            transform: Additional image transforms
        """
        self.data_root = data_root
        self.split = split
        self.image_size = image_size
        self.heatmap_size = heatmap_size
        self.sigma = sigma
        self.sequence_length = sequence_length
        self.num_keypoints = len(self.KEYPOINT_NAMES)
        self.num_pafs = len(self.SKELETON) * 2  # x and y components
        
        # Load annotations
        self.annotations = self._load_annotations()
        self.sequences = self._organize_sequences()
        
        # Default transforms
        if transform is None:
            self.transform = transforms.Compose([
                transforms.ToPILImage(),
                transforms.Resize(image_size),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
        else:
            self.transform = transform
    
    def _load_annotations(self):
        """Load PoseTrack annotations"""
        ann_dir = os.path.join(self.data_root, 'posetrack_data', self.split)
        annotations = {}
        
        for ann_file in os.listdir(ann_dir):
            if ann_file.endswith('.json'):
                ann_path = os.path.join(ann_dir, ann_file)
                with open(ann_path, 'r') as f:
                    data = json.load(f)
                
                vid_id = ann_file.split('_')[0]
                annotations[vid_id] = data
        
        return annotations
    
    def _organize_sequences(self):
        """Organize annotations into sequences"""
        sequences = []

        for vid_id, ann_data in self.annotations.items():
            # Group images by image_id (not frame_id)
            images = {img['image_id']: img for img in ann_data['images']}
            annotations = defaultdict(list)

            # Group annotations by image_id
            for ann in ann_data['annotations']:
                image_id = ann['image_id']
                annotations[image_id].append(ann)

            # Get labeled frames only (frames with annotations)
            labeled_image_ids = []
            for img in ann_data['images']:
                if img.get('is_labeled', False) and img.get('has_labeled_person', False):
                    labeled_image_ids.append(img['image_id'])

            # Sort by image_id to maintain temporal order
            labeled_image_ids = sorted(labeled_image_ids)

            # Create sequences from labeled frames
            for i in range(0, len(labeled_image_ids) - self.sequence_length + 1, 1):
                seq_image_ids = labeled_image_ids[i:i + self.sequence_length]

                # Check if all frames have annotations
                valid_sequence = True
                for image_id in seq_image_ids:
                    if image_id not in annotations or len(annotations[image_id]) == 0:
                        valid_sequence = False
                        break

                if valid_sequence:
                    sequences.append({
                        'vid_id': vid_id,
                        'image_ids': seq_image_ids,  # Changed from frame_ids to image_ids
                        'images': images,
                        'annotations': annotations
                    })

        return sequences
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = self.sequences[idx]

        images = []
        heatmap_targets = []
        displacement_targets = []
        keypoint_track_ids = []
        visibility_targets = []
        location_targets = []
        association_targets = []
        person_boxes = []
        person_track_ids = []

        for image_id in sequence['image_ids']:  # Changed from frame_ids to image_ids
            # Load image
            img_info = sequence['images'][image_id]
            img_path = os.path.join(self.data_root, img_info['file_name'])

            # Check if image file exists
            if not os.path.exists(img_path):
                print(f"Warning: Image not found: {img_path}")
                # Create dummy image
                image = np.zeros((480, 640, 3), dtype=np.uint8)
            else:
                image = cv2.imread(img_path)
                if image is None:
                    print(f"Warning: Failed to load image: {img_path}")
                    image = np.zeros((480, 640, 3), dtype=np.uint8)
                else:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # Get annotations for this frame
            frame_annotations = sequence['annotations'][image_id]

            # Generate targets
            (heatmap, displacement, kp_track_ids, visibility, locations,
             association, boxes, p_track_ids) = self._generate_targets(image, frame_annotations)

            # Apply transforms
            if self.transform:
                image = self.transform(image)

            images.append(image)
            heatmap_targets.append(heatmap)
            displacement_targets.append(displacement)
            keypoint_track_ids.append(kp_track_ids)
            visibility_targets.append(visibility)
            location_targets.append(locations)
            association_targets.append(association)
            person_boxes.append(boxes)
            person_track_ids.extend(p_track_ids)

        return {
            'images': torch.stack(images),
            'heatmap_targets': torch.stack(heatmap_targets),
            'displacement_targets': torch.stack(displacement_targets),
            'keypoint_track_ids': torch.stack(keypoint_track_ids),
            'visibility_targets': torch.stack(visibility_targets),
            'location_targets': torch.stack(location_targets),
            'association_targets': torch.stack(association_targets),
            'person_boxes': person_boxes,
            'person_track_ids': torch.tensor(person_track_ids, dtype=torch.long),
            'vid_id': sequence['vid_id'],
            'image_ids': sequence['image_ids']  # Changed from frame_ids to image_ids
        }
    
    def _generate_targets(self, image, annotations):
        """Generate training targets from annotations for keypoint-level tracking"""
        h, w = image.shape[:2]
        hm_h, hm_w = self.heatmap_size

        # Initialize target maps
        keypoint_heatmap = np.zeros((self.num_keypoints, hm_h, hm_w), dtype=np.float32)

        # Initialize keypoint-level tracking targets
        keypoint_track_ids = np.full((self.num_keypoints,), -1, dtype=np.int64)  # -1 for no detection
        keypoint_visibility = np.zeros((self.num_keypoints,), dtype=np.float32)
        keypoint_locations = np.full((self.num_keypoints, 2), -1, dtype=np.float32)  # [x, y] for each keypoint

        person_boxes = []
        person_track_ids = []

        for ann in annotations:
            if 'keypoints' not in ann or 'bbox' not in ann:
                continue

            # Get keypoints and bbox
            keypoints = np.array(ann['keypoints']).reshape(-1, 3)
            bbox = ann['bbox']  # [x, y, w, h]
            track_id = ann.get('track_id', ann.get('id', 0))  # Use id if track_id not available
            person_id = ann.get('person_id', track_id)

            # Scale coordinates to heatmap size
            scale_x = hm_w / w
            scale_y = hm_h / h

            scaled_keypoints = keypoints.copy()
            scaled_keypoints[:, 0] *= scale_x
            scaled_keypoints[:, 1] *= scale_y

            # Generate keypoint heatmaps and tracking targets
            for i, (x, y, v) in enumerate(scaled_keypoints):
                if v > 0 and i < self.num_keypoints:  # visible keypoint and valid index
                    # Generate heatmap
                    self._generate_gaussian_heatmap(
                        keypoint_heatmap[i], x, y, self.sigma
                    )

                    # Set tracking targets for this keypoint (keep within reasonable bounds)
                    keypoint_track_ids[i] = (person_id % 5) * 17 + i  # Unique ID per keypoint per person
                    keypoint_visibility[i] = 1.0
                    keypoint_locations[i] = [x, y]

            # Store person info
            person_boxes.append([bbox[0], bbox[1], bbox[0] + bbox[2], bbox[1] + bbox[3]])
            person_track_ids.append(track_id)

        # Generate displacement targets (simplified - could be improved with optical flow)
        displacement_targets = np.zeros((self.num_keypoints, 2, hm_h, hm_w), dtype=np.float32)

        # Generate association targets (simplified - 1 if same person, 0 otherwise)
        association_targets = np.ones((self.num_keypoints,), dtype=np.float32)

        return (
            torch.from_numpy(keypoint_heatmap),
            torch.from_numpy(displacement_targets),
            torch.tensor(keypoint_track_ids, dtype=torch.long),
            torch.tensor(keypoint_visibility, dtype=torch.float32),
            torch.tensor(keypoint_locations, dtype=torch.float32),
            torch.tensor(association_targets, dtype=torch.float32),
            torch.tensor(person_boxes, dtype=torch.float32) if person_boxes else torch.zeros((0, 4)),
            person_track_ids
        )
    
    def _generate_gaussian_heatmap(self, heatmap, center_x, center_y, sigma):
        """Generate Gaussian heatmap for a keypoint"""
        h, w = heatmap.shape
        x = np.arange(0, w, 1, np.float32)
        y = np.arange(0, h, 1, np.float32)
        y = y[:, np.newaxis]
        
        # Generate Gaussian
        gaussian = np.exp(-((x - center_x) ** 2 + (y - center_y) ** 2) / (2 * sigma ** 2))
        
        # Add to heatmap (take maximum to handle overlapping)
        heatmap[:] = np.maximum(heatmap, gaussian)
    

