#!/bin/bash

# PoseTrack Training Script

# Set environment variables
export CUDA_VISIBLE_DEVICES=0

# Training parameters
DATA_ROOT="./datasets/PoseTrack21/data"
OUTPUT_DIR="./outputs/posetrack_$(date +%Y%m%d_%H%M%S)"
BATCH_SIZE=8
NUM_WORKERS=4
EPOCHS=100
LEARNING_RATE=1e-4

# Create output directory
mkdir -p $OUTPUT_DIR

# Run training
python src/train.py \
    --data_root $DATA_ROOT \
    --output_dir $OUTPUT_DIR \
    --batch_size $BATCH_SIZE \
    --num_workers $NUM_WORKERS \
    --epochs $EPOCHS \
    --lr $LEARNING_RATE \
    --weight_decay 1e-4 \
    --lr_schedule cosine \
    --warmup_epochs 5 \
    --keypoint_weight 1.0 \
    --paf_weight 1.0 \
    --instance_weight 0.5 \
    --tracking_weight 0.5 \
    --contrastive_weight 0.3 \
    --save_freq 10 \
    --eval_freq 5 \
    --mixed_precision \
    --backbone_pretrained

echo "Training completed. Results saved to: $OUTPUT_DIR"
