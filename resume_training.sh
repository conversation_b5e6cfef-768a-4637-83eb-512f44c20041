#!/bin/bash

# Resume Training Script for PoseTrack

# Set environment variables
export CUDA_VISIBLE_DEVICES=0

# Training parameters
DATA_ROOT="./datasets/PoseTrack21/data"
OUTPUT_DIR="./outputs/posetrack_20250611_074246"  # Your existing output directory
BATCH_SIZE=2
NUM_WORKERS=4
EPOCHS=100
LEARNING_RATE=1e-4

echo "Resuming training from: $OUTPUT_DIR"

# Run training with auto-resume (will automatically find latest checkpoint)
python src/train.py \
    --data_root $DATA_ROOT \
    --output_dir $OUTPUT_DIR \
    --batch_size $BATCH_SIZE \
    --num_workers $NUM_WORKERS \
    --epochs $EPOCHS \
    --lr $LEARNING_RATE \
    --weight_decay 1e-4 \
    --lr_schedule cosine \
    --warmup_epochs 5 \
    --keypoint_weight 1.0 \
    --paf_weight 1.0 \
    --instance_weight 0.5 \
    --tracking_weight 0.5 \
    --contrastive_weight 0.3 \
    --save_freq 5 \
    --eval_freq 2 \
    --vis_freq 200 \
    --mixed_precision \
    --backbone_pretrained

echo "Training resumed and completed. Results saved to: $OUTPUT_DIR"
