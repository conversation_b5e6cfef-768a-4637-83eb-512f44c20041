# PoseTrack Model Configuration

# Dataset Configuration
dataset:
  name: "PoseTrack21"
  data_root: "./datasets/PoseTrack21/data"
  num_keypoints: 17
  image_size: [512, 512]
  heatmap_size: [128, 128]
  sequence_length: 3
  sigma: 2

# Model Configuration
model:
  backbone: "resnet50"
  pretrained: true
  embed_dim: 128
  fpn_channels: 256

# Training Configuration
training:
  batch_size: 8
  num_workers: 4
  epochs: 100
  learning_rate: 1e-4
  weight_decay: 1e-4
  lr_schedule: "cosine"
  warmup_epochs: 5
  mixed_precision: true

# Loss Configuration
loss:
  keypoint_weight: 1.0
  paf_weight: 1.0
  instance_weight: 0.5
  tracking_weight: 0.5
  contrastive_weight: 0.3

# Inference Configuration
inference:
  confidence_threshold: 0.1
  nms_threshold: 0.5
  max_persons: 20

# Evaluation Configuration
evaluation:
  oks_threshold: 0.5
  distance_threshold: 50
  eval_freq: 5

# Checkpoint Configuration
checkpoint:
  output_dir: "./outputs"
  save_freq: 10
  resume: null

# Device Configuration
device:
  use_cuda: true
  device_id: 0
