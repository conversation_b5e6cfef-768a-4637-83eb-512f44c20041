#!/usr/bin/env python3
"""
Test script for training pipeline
"""

import os
import sys
sys.path.append('src')

import torch
from torch.utils.data import DataLoader
from models.pose_track_model import KeypointPoseTrackModel, KeypointTrackingLoss
from datasets.posetrack_dataset import PoseTrackDataset

def test_training():
    """Test the training pipeline"""
    
    # Check if dataset exists
    data_root = "./datasets/PoseTrack21/data"
    if not os.path.exists(data_root):
        print(f"Dataset not found at {data_root}")
        return
    
    try:
        print("Creating dataset...")
        dataset = PoseTrackDataset(
            data_root=data_root,
            split='train',
            image_size=(256, 256),  # Smaller size for testing
            heatmap_size=(64, 64),
            sequence_length=2  # Shorter sequence for testing
        )
        
        print(f"Dataset created with {len(dataset)} sequences")
        
        if len(dataset) == 0:
            print("No sequences found in dataset")
            return
        
        # Create data loader
        data_loader = DataLoader(
            dataset,
            batch_size=1,  # Small batch for testing
            shuffle=False,
            num_workers=0  # No multiprocessing for testing
        )
        
        print("Creating model...")
        model = KeypointPoseTrackModel(
            num_keypoints=17,
            embed_dim=32,  # Smaller embedding for testing
            backbone_pretrained=False,
            max_track_ids=100
        )
        
        criterion = KeypointTrackingLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
        
        print("Testing training step...")
        model.train()
        
        # Get one batch
        batch = next(iter(data_loader))
        
        images = batch['images']  # [B, T, 3, H, W]
        heatmap_targets = batch['heatmap_targets']
        displacement_targets = batch['displacement_targets']
        keypoint_track_ids = batch['keypoint_track_ids']
        visibility_targets = batch['visibility_targets']
        association_targets = batch['association_targets']
        
        print(f"Batch shapes:")
        print(f"  Images: {images.shape}")
        print(f"  Heatmap targets: {heatmap_targets.shape}")
        print(f"  Track IDs: {keypoint_track_ids.shape}")
        
        batch_size, seq_len = images.shape[:2]
        
        # Reset model tracking state
        model.reset_tracking_state()
        
        total_loss = 0
        
        for t in range(seq_len):
            frame_images = images[:, t]
            frame_heatmap_targets = heatmap_targets[:, t]
            frame_displacement_targets = displacement_targets[:, t]
            frame_track_ids = keypoint_track_ids[:, t]
            frame_visibility = visibility_targets[:, t]
            frame_association = association_targets[:, t]
            
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(frame_images, mode='train')
            
            targets = {
                'heatmap_targets': frame_heatmap_targets,
                'displacement_targets': frame_displacement_targets,
                'keypoint_track_ids': frame_track_ids,
                'track_id_targets': frame_track_ids,
                'visibility_targets': frame_visibility,
                'association_targets': frame_association
            }
            
            losses = criterion(outputs, targets)
            loss = losses['total_loss']
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            
            print(f"Frame {t}: Loss = {loss.item():.4f}")
            for loss_name, loss_value in losses.items():
                if loss_name != 'total_loss':
                    print(f"  {loss_name}: {loss_value.item():.4f}")
        
        avg_loss = total_loss / seq_len
        print(f"\nAverage loss: {avg_loss:.4f}")
        print("Training test completed successfully! ✅")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_training()
