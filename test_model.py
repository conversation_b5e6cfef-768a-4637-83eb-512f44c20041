#!/usr/bin/env python3
"""
Test script for KeypointPoseTrackModel
"""

import os
import sys
sys.path.append('src')

import torch
from models.pose_track_model import KeypointPoseTrackModel, KeypointTrackingLoss

def test_model():
    """Test the model"""
    
    try:
        # Create model
        print("Creating model...")
        model = KeypointPoseTrackModel(
            num_keypoints=17,
            embed_dim=64,
            backbone_pretrained=False,  # Don't download pretrained weights for test
            max_track_ids=1000
        )
        
        print("Model created successfully!")
        print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # Create dummy input
        batch_size = 2
        height, width = 512, 512
        images = torch.randn(batch_size, 3, height, width)
        
        print(f"Input shape: {images.shape}")
        
        # Forward pass
        print("Running forward pass...")
        model.eval()
        with torch.no_grad():
            outputs = model(images, mode='inference')
        
        print("Forward pass successful!")
        print("Output keys:", list(outputs.keys()))
        print("Heatmaps shape:", outputs['heatmaps'].shape)
        print("Embeddings shape:", outputs['embeddings'].shape)
        print("Track ID logits shape:", outputs['track_id_logits'].shape)
        print("Visibility scores shape:", outputs['visibility_scores'].shape)
        
        # Test loss function
        print("\nTesting loss function...")
        criterion = KeypointTrackingLoss()
        
        # Create dummy targets
        targets = {
            'heatmap_targets': torch.randn(batch_size, 17, height, width),
            'displacement_targets': torch.randn(batch_size, 17, 2, 128, 128),
            'keypoint_track_ids': torch.randint(0, 100, (batch_size, 17)),
            'track_id_targets': torch.randint(0, 1000, (batch_size, 17)),
            'visibility_targets': torch.rand(batch_size, 17),
            'association_targets': torch.rand(batch_size, 17)
        }
        
        # Compute loss
        losses = criterion(outputs, targets)
        print("Loss computation successful!")
        print("Loss keys:", list(losses.keys()))
        print("Total loss:", losses['total_loss'].item())
        
        print("\nAll tests passed! ✅")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_model()
