# Bottom-up Pose Estimation and Tracking Model

이 프로젝트는 PoseTrack21 데이터셋을 사용하여 bottom-up 방식의 pose estimation과 tracking을 동시에 수행하는 딥러닝 모델을 구현합니다.

## 🏗️ 모델 아키텍처

### 전체 구조
- **Backbone**: ResNet-50 (ImageNet pretrained)
- **Feature Pyramid Network (FPN)**: 멀티스케일 특징 융합
- **Pose Head**: Keypoint heatmap + Part Affinity Fields (PAF) + Instance segmentation
- **Tracking Head**: Temporal embedding + Association network

### 주요 특징
1. **Bottom-up Approach**: 먼저 모든 keypoint를 검출한 후 person instance로 그룹화
2. **Multi-task Learning**: Pose estimation과 tracking을 동시에 학습
3. **Temporal Consistency**: 시간적 정보를 활용한 robust tracking
4. **End-to-end Training**: 전체 파이프라인을 한 번에 학습

## 📁 프로젝트 구조

```
pktrack/
├── src/
│   ├── models/
│   │   ├── backbone.py          # ResNet backbone
│   │   ├── heads.py             # FPN, PoseHead, TrackingHead
│   │   └── pose_track_model.py  # 메인 모델
│   ├── datasets/
│   │   └── posetrack_dataset.py # PoseTrack21 데이터 로더
│   ├── losses/
│   │   └── pose_track_loss.py   # 멀티태스크 손실 함수
│   ├── utils/
│   │   ├── metrics.py           # 평가 메트릭
│   │   └── inference.py         # 추론 파이프라인
│   ├── train.py                 # 학습 스크립트
│   └── evaluate.py              # 평가 스크립트
├── datasets/
│   └── PoseTrack21/             # PoseTrack21 데이터셋
├── config.yaml                  # 설정 파일
├── requirements.txt             # 의존성 패키지
├── run_train.sh                 # 학습 실행 스크립트
└── README.md
```

## 🚀 설치 및 설정

### 1. 환경 설정
```bash
# 가상환경 생성 (선택사항)
conda create -n posetrack python=3.8
conda activate posetrack

# 의존성 설치
pip install -r requirements.txt
```

### 2. 데이터셋 준비
PoseTrack21 데이터셋을 다운로드하고 다음 구조로 배치:
```
datasets/PoseTrack21/data/
├── images/
│   ├── train/
│   └── val/
└── posetrack_data/
    ├── train/
    └── val/
```

## 🏃‍♂️ 학습

### 기본 학습
```bash
# 스크립트 실행
./run_train.sh

# 또는 직접 실행
python src/train.py \
    --data_root ./datasets/PoseTrack21/data \
    --output_dir ./outputs \
    --batch_size 8 \
    --epochs 100 \
    --lr 1e-4 \
    --mixed_precision
```

### 주요 하이퍼파라미터
- `batch_size`: 배치 크기 (기본값: 8)
- `lr`: 학습률 (기본값: 1e-4)
- `epochs`: 학습 에포크 (기본값: 100)
- `keypoint_weight`: Keypoint loss 가중치 (기본값: 1.0)
- `paf_weight`: PAF loss 가중치 (기본값: 1.0)
- `tracking_weight`: Tracking loss 가중치 (기본값: 0.5)

## 📊 평가

```bash
python src/evaluate.py \
    --checkpoint ./outputs/best_model.pth \
    --data_root ./datasets/PoseTrack21/data \
    --split val \
    --output_dir ./eval_results \
    --save_predictions \
    --visualize
```

### 평가 메트릭
- **Pose Estimation**: mAP (OKS), PCK
- **Tracking**: MOTA, IDF1

## 🔧 모델 세부사항

### 1. Backbone (ResNet-50)
- ImageNet pretrained weights 사용
- Multi-scale feature extraction (C2, C3, C4, C5)
- Feature channels: 256, 512, 1024, 2048

### 2. Feature Pyramid Network
- Top-down pathway로 고해상도 특징 생성
- 모든 레벨에서 256 채널로 통일
- 1/4 스케일 특징을 메인으로 사용

### 3. Pose Head
- **Keypoint Branch**: 17개 keypoint heatmap 생성
- **PAF Branch**: 19개 limb connection의 방향 벡터 생성
- **Instance Branch**: Person instance segmentation

### 4. Tracking Head
- Pose-aware feature extraction
- Temporal embedding (128차원)
- Triplet loss + Contrastive loss로 학습

### 5. Loss Functions
```python
Total Loss = λ₁ × Keypoint Loss + λ₂ × PAF Loss + 
             λ₃ × Instance Loss + λ₄ × Tracking Loss + 
             λ₅ × Contrastive Loss
```

## 🎯 주요 기능

### 1. Bottom-up Pose Estimation
- 모든 keypoint를 먼저 검출
- PAF를 사용하여 keypoint를 person instance로 그룹화
- Instance segmentation으로 person 영역 식별

### 2. Temporal Tracking
- Person-level embedding 학습
- Hungarian algorithm 기반 association
- Track ID 일관성 유지

### 3. Multi-task Learning
- Pose estimation과 tracking 동시 학습
- Shared backbone으로 효율적인 특징 추출
- Task-specific head로 전문화된 출력

## 📈 성능 최적화

### 1. 학습 최적화
- Mixed precision training 지원
- Cosine annealing learning rate schedule
- Gradient clipping으로 안정적 학습

### 2. 추론 최적화
- Efficient keypoint grouping algorithm
- Temporal consistency를 위한 track management
- GPU 메모리 효율적 처리

## 🔍 실험 결과

### PoseTrack21 Validation Set
| Metric | Value |
|--------|-------|
| mAP    | TBD   |
| PCK    | TBD   |
| MOTA   | TBD   |
| IDF1   | TBD   |

*실제 학습 후 결과로 업데이트 예정*

## 🛠️ 커스터마이징

### 1. 새로운 데이터셋 추가
`src/datasets/` 폴더에 새로운 데이터셋 클래스 구현

### 2. 모델 아키텍처 수정
`src/models/` 폴더의 해당 모듈 수정

### 3. 손실 함수 추가
`src/losses/` 폴더에 새로운 손실 함수 구현

## 📚 참고 문헌

1. PoseTrack: A Benchmark for Human Pose Estimation and Tracking
2. OpenPose: Realtime Multi-Person 2D Pose Estimation using Part Affinity Fields
3. Deep High-Resolution Representation Learning for Human Pose Estimation
4. FairMOT: On the Fairness of Detection and Re-Identification in Multiple Object Tracking

## 🤝 기여

이슈나 개선사항이 있으시면 언제든지 제보해 주세요!

## 📄 라이선스

MIT License

---

**연구원을 위한 추가 정보:**

이 구현은 최신 pose estimation과 tracking 기법들을 결합한 end-to-end 학습 가능한 모델입니다. 특히 bottom-up 방식의 장점인 확장성과 top-down 방식의 정확성을 모두 활용하려고 했습니다. 

실제 연구에서는 다음과 같은 추가 실험을 고려해볼 수 있습니다:
- 다양한 backbone 네트워크 비교 (HRNet, EfficientNet 등)
- Attention mechanism 추가
- Graph Neural Network 기반 keypoint association
- Transformer 기반 temporal modeling
