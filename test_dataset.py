#!/usr/bin/env python3
"""
Test script for PoseTrack dataset loader
"""

import os
import sys
sys.path.append('src')

from datasets.posetrack_dataset import PoseTrackDataset
import torch

def test_dataset():
    """Test the dataset loader"""
    
    # Check if dataset exists
    data_root = "./datasets/PoseTrack21/data"
    if not os.path.exists(data_root):
        print(f"Dataset not found at {data_root}")
        return
    
    try:
        # Create dataset
        print("Creating dataset...")
        dataset = PoseTrackDataset(
            data_root=data_root,
            split='train',
            image_size=(512, 512),
            heatmap_size=(128, 128),
            sequence_length=3
        )
        
        print(f"Dataset created successfully!")
        print(f"Number of sequences: {len(dataset)}")
        
        if len(dataset) > 0:
            # Test loading one sample
            print("Loading first sample...")
            sample = dataset[0]
            
            print("Sample keys:", list(sample.keys()))
            print("Images shape:", sample['images'].shape)
            print("Heatmap targets shape:", sample['heatmap_targets'].shape)
            print("Keypoint track IDs shape:", sample['keypoint_track_ids'].shape)
            print("Visibility targets shape:", sample['visibility_targets'].shape)
            
            print("Sample loaded successfully!")
        else:
            print("No sequences found in dataset")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_dataset()
