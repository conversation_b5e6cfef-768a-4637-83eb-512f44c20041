#!/usr/bin/env python3
"""
Demo script for PoseTrack model inference
"""

import os
import argparse
import cv2
import torch
import numpy as np
from pathlib import Path

from src.models.pose_track_model import KeypointPoseTrackModel
from src.datasets.posetrack_dataset import PoseTrackDataset
from src.utils.inference import PoseTrackInference
from src.utils.visualization import visualize_pose_predictions


def parse_args():
    parser = argparse.ArgumentParser(description='PoseTrack Demo')
    
    parser.add_argument('--input', type=str, required=True,
                       help='Input video file or image directory')
    parser.add_argument('--checkpoint', type=str, required=True,
                       help='Path to model checkpoint')
    parser.add_argument('--output', type=str, default='./demo_output',
                       help='Output directory')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use')
    parser.add_argument('--confidence', type=float, default=0.3,
                       help='Confidence threshold')
    parser.add_argument('--save_video', action='store_true',
                       help='Save output as video')
    
    return parser.parse_args()


def load_model(checkpoint_path, device):
    """Load trained model"""
    print(f'Loading model from {checkpoint_path}')
    
    # Create model
    model = KeypointPoseTrackModel(
        num_keypoints=17,
        embed_dim=64,
        backbone_pretrained=False,
        max_track_ids=1000
    )
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    print(f'Model loaded successfully')
    return model


def process_video(video_path, model, device, args):
    """Process video file"""
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        raise ValueError(f"Cannot open video: {video_path}")
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f'Video: {width}x{height}, {fps} FPS, {total_frames} frames')
    
    # Create inference engine
    inference_engine = PoseTrackInference(
        model, device, 
        confidence_threshold=args.confidence
    )
    
    # Prepare output
    os.makedirs(args.output, exist_ok=True)
    
    if args.save_video:
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out_video = cv2.VideoWriter(
            os.path.join(args.output, 'output.mp4'),
            fourcc, fps, (width, height)
        )
    
    frame_idx = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Process frame
        results = inference_engine.process_frame(frame)

        # Visualize results
        vis_frame = visualize_results(frame, results['persons'])

        # Alternative: use the visualization utility
        # vis_frame = visualize_pose_predictions(frame, results, frame_path)
        
        # Save frame
        frame_path = os.path.join(args.output, f'frame_{frame_idx:06d}.jpg')
        cv2.imwrite(frame_path, vis_frame)
        
        if args.save_video:
            out_video.write(vis_frame)
        
        frame_idx += 1
        
        if frame_idx % 100 == 0:
            print(f'Processed {frame_idx}/{total_frames} frames')
    
    cap.release()
    if args.save_video:
        out_video.release()
    
    print(f'Processing completed. Results saved to {args.output}')


def process_images(image_dir, model, device, args):
    """Process directory of images"""
    image_paths = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
        image_paths.extend(Path(image_dir).glob(ext))
    
    image_paths = sorted(image_paths)
    
    if not image_paths:
        raise ValueError(f"No images found in {image_dir}")
    
    print(f'Found {len(image_paths)} images')
    
    # Create inference engine
    inference_engine = PoseTrackInference(
        model, device,
        confidence_threshold=args.confidence
    )
    
    # Prepare output
    os.makedirs(args.output, exist_ok=True)
    
    for i, image_path in enumerate(image_paths):
        # Load image
        image = cv2.imread(str(image_path))
        if image is None:
            print(f'Warning: Cannot load {image_path}')
            continue
        
        # Process image
        results = inference_engine.process_frame(image)
        
        # Visualize results
        vis_image = visualize_results(image, results['persons'])
        
        # Save result
        output_path = os.path.join(args.output, f'result_{i:06d}.jpg')
        cv2.imwrite(output_path, vis_image)
        
        if (i + 1) % 50 == 0:
            print(f'Processed {i + 1}/{len(image_paths)} images')
    
    print(f'Processing completed. Results saved to {args.output}')


def visualize_results(image, persons):
    """Visualize pose estimation and tracking results"""
    vis_image = image.copy()
    
    # Define colors for different tracks
    colors = [
        (0, 255, 0),    # Green
        (255, 0, 0),    # Blue  
        (0, 0, 255),    # Red
        (255, 255, 0),  # Cyan
        (255, 0, 255),  # Magenta
        (0, 255, 255),  # Yellow
        (128, 0, 128),  # Purple
        (255, 165, 0),  # Orange
    ]
    
    # PoseTrack skeleton connections
    skeleton = [
        [16, 14], [14, 12], [17, 15], [15, 13], [12, 13],
        [6, 12], [7, 13], [6, 7], [6, 8], [7, 9],
        [8, 10], [9, 11], [2, 3], [1, 2], [1, 3],
        [2, 4], [3, 5], [4, 6], [5, 7]
    ]
    
    for person in persons:
        track_id = person.get('track_id', 0)
        color = colors[track_id % len(colors)]
        
        # Collect keypoints
        keypoints = [None] * 17
        for kp_type in range(17):
            if kp_type in person:
                kp = person[kp_type]
                x, y = int(kp['x']), int(kp['y'])
                keypoints[kp_type] = (x, y)
                
                # Draw keypoint
                cv2.circle(vis_image, (x, y), 4, color, -1)
                cv2.circle(vis_image, (x, y), 6, (255, 255, 255), 2)
        
        # Draw skeleton
        for connection in skeleton:
            pt1_idx, pt2_idx = connection[0] - 1, connection[1] - 1  # Convert to 0-based
            
            if (pt1_idx < len(keypoints) and pt2_idx < len(keypoints) and
                keypoints[pt1_idx] is not None and keypoints[pt2_idx] is not None):
                
                pt1, pt2 = keypoints[pt1_idx], keypoints[pt2_idx]
                cv2.line(vis_image, pt1, pt2, color, 3)
        
        # Draw track ID
        if keypoints[0] is not None:  # Use nose position
            text_pos = (keypoints[0][0] - 10, keypoints[0][1] - 15)
            cv2.putText(vis_image, f'ID:{track_id}', text_pos,
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            
            # Draw background for text
            text_size = cv2.getTextSize(f'ID:{track_id}', cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
            cv2.rectangle(vis_image, 
                         (text_pos[0] - 2, text_pos[1] - text_size[1] - 2),
                         (text_pos[0] + text_size[0] + 2, text_pos[1] + 2),
                         (0, 0, 0), -1)
            cv2.putText(vis_image, f'ID:{track_id}', text_pos,
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
    
    # Add info text
    info_text = f'Persons: {len(persons)}'
    cv2.putText(vis_image, info_text, (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    return vis_image


def main():
    args = parse_args()
    
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')
    
    # Load model
    model = load_model(args.checkpoint, device)
    
    # Check input type
    if os.path.isfile(args.input):
        # Video file
        print(f'Processing video: {args.input}')
        process_video(args.input, model, device, args)
    elif os.path.isdir(args.input):
        # Image directory
        print(f'Processing images in: {args.input}')
        process_images(args.input, model, device, args)
    else:
        raise ValueError(f'Input path does not exist: {args.input}')


if __name__ == '__main__':
    main()
